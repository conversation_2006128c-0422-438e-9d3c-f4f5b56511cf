@use '../utils' as *;

/*----------------------------------------*/
/* Animations styles
/*----------------------------------------*/
// Animate ripple
@keyframes popupBtn {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }

  50% {
    transform: scale(1.4);
    opacity: 0.3;
  }

  100% {
    transform: scale(2);
    opacity: 0;
  }
}

// sticky-animation
@keyframes sticky {
  0% {
    transform: translateY(-100%);
  }

  100% {
    transform: translateY(0%);
  }
}

@keyframes tdSpinner {
  from {
    -webkit-transform: rotate(0turn);
    transform: rotate(0turn);
  }

  to {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn);
  }
}

@keyframes tada {
  0% {
    transform: scaleX(1);
  }

  10% {
    transform: scale3d(.9, .9, .9) rotate(-3deg);
  }

  20% {
    transform: scale3d(.9, .9, .9) rotate(-3deg);
  }

  30% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(3deg);
  }

  50% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(3deg);
  }

  70% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(3deg);
  }

  90% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(3deg);
  }

  40% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(-3deg);
  }

  60% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(-3deg);
  }

  80% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(-3deg);
  }

  to {
    transform: scaleX(1);
  }
}

.tada {
  animation-name: tada;
}

// upDown
@keyframes upDown {
  0% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(-15px);
  }
}

// rotateHammer
@keyframes rotateHammer {
  0% {
    transform: rotate(15deg);
  }

  50% {
    transform: rotate(-90deg);
  }

  100% {
    transform: rotate(15deg);
  }
}

@-webkit-keyframes rotateHammer {
  0% {
    -webkit-transform: rotate(15deg);
  }

  50% {
    -webkit-transform: rotate(-90deg);
  }

  100% {
    -webkit-transform: rotate(15deg);
  }
}

@keyframes float {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(120vw);
  }
}