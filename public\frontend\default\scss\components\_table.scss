@use '../utils' as *;

/*----------------------------------------*/
/* Table styles
/*----------------------------------------*/

// Table container
.table-container {
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.05) -31.74%, rgba(9, 70, 255, 0.03) 88.89%);
    border: 1px solid rgba($danger, $alpha: 0.06);
    border-radius: 24px;

    .table-heading {
        margin-bottom: 20px;
        padding: 0 16px;

        .title {
            font-size: 20px;
        }
    }
}

// Table description
.table-description {
    display: flex;
    align-items: center;
    gap: rem(10);

    .icon {
        display: flex;
        align-items: center;
        gap: 0.625rem;
        background: linear-gradient(90deg, rgba(71, 118, 230, 0.16) 0%, rgba(142, 84, 233, 0.16) 100%);
        border-radius: 30px;
        justify-content: center;
        width: 44px;
        height: 44px;
        position: relative;
        font-size: 18px;
        color: var(--td-white);
        flex: 0 0 auto;

        &::before {
            position: absolute;
            content: "";
            inset: 0;
            padding: 1px;
            -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            border-radius: 24px;
            z-index: -1;

            @include dark-theme {
                background: linear-gradient(90deg, rgba(71, 118, 230, 1) 0%, rgba(142, 84, 233, 1) 100%);
            }
        }

        span {
            display: inline-flex;
            align-items: center;
            color: rgba($heading, $alpha: 1);
            font-size: 20px;

            @include dark-theme {
                color: rgba($white, $alpha: 1);
            }
        }
    }

    .contents {
        .title {
            font-size: 14px;
            font-family: var(--td-ff-body);
            font-weight: 700;
        }

        .date {
            font-size: rem(12);

            span {
                margin-inline-end: 4px;
            }
        }
    }

    &.is-primary-10 {
        .icon {
            background: rgba($primary, $alpha: 0.10);
            color: var(--td-danger);
        }
    }

    &.is-danger-10 {
        .icon {
            background: rgba($danger, $alpha: 0.10);
            color: var(--td-danger);
        }
    }
}

// Common table
.td-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;

    &.recent-table {
        min-width: 1280px;
        position: relative;
        z-index: 1;

        &::before {
            position: absolute;
            content: "";
            inset: 0;
            padding: 1px;
            background: linear-gradient(90deg, rgba($heading, $alpha: 0.16) 0%, rgba($heading, $alpha: 0.16) 100%);
            -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            border-radius: 24px;
            z-index: -1;

            @include dark-theme {
                background: linear-gradient(180deg, rgba(11, 39, 122, 1) 0%, rgba(0, 148, 255, 1) 100%);
            }
        }
    }

    thead {
        background: rgb(221 221 221 / 50%);
        @include border-radius(8px);

        @include dark-theme {
            background: rgba($white, $alpha: 0.08);
        }

        overflow: hidden; // Ensures border-radius applies in Firefox

        tr {
            th {
                border-inline-start: 0;
                border-inline-end: 0;
                padding: 12px 16px;
                text-align: left;
                font-weight: 600;
                color: var(--td-text-primary);

                @include rtl {
                    text-align: right;
                }

                @include dark-theme {
                    color: var(--td-white);
                }
            }
        }
    }

    th {
        text-align: left;
        padding: 14px 16px;
        font-weight: 600;
        color: var(--td-text-secondary);
    }

    td {
        text-align: left;
        padding: 13px 16px;
        border-bottom: 1px solid rgba($heading, $alpha: 0.16);
        font-weight: 500;

        @include dark-theme {
            border-color: rgba($white, $alpha: 0.06);
        }
    }

    tr {
        &:last-child {
            td {
                border-bottom: none;
            }
        }
    }

    tbody {
        tr {
            &:hover {
                // background: rgba($white, $alpha: 0.08);
            }
        }
    }
}

// Table actions
.table-actions {
    display: flex;
    align-items: center;
    gap: 10px;

    @include rtl {
        justify-content: end;
    }

    .td-btn {
        min-width: 72px;
    }

    .icon {
        background: var(--td-primary);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 28px;
        height: 28px;
        color: var(--td-white);

        &.is-green {
            background: var(--td-green)
        }

        &.is-outline {
            border: 1px solid var(--td-text-secondary);
            background-color: transparent;
            color: var(--td-text-secondary);
        }
    }

    .text {
        background: var(--td-primary);
        border-radius: 8px;
        padding: 7px 16px 7px 16px;
        display: flex;
        flex-direction: row;
        gap: 10px;
        align-items: center;
        justify-content: center;
        position: relative;
        font-size: 14px;
        font-weight: 700;
        color: var(--td-white);
        line-height: 1;

        &.is-danger {
            background: #eb4e5c;
        }

        &.is-primary {
            color: var(--td-heading);
            background: var(--td-primary)
        }
    }
}

// Filter bar
.filter-bar {
    padding: 0 16px 16px;
    display: flex;
    align-items: center;
    gap: 16px 16px;
    flex-wrap: wrap;

    @media #{$xs} {
        display: grid;
        grid-template-columns: 1fr;
    }

    .clip-path {
        position: relative;
        display: inline-block;
        padding: 1px;
        width: 100%;

        &::before,
        &::after {
            position: absolute;
            inset-inline-start: 0;
            top: 0;
            transition: all .3s;
            width: 100%;
            height: 100%;
            background: linear-gradient(to right, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1));
            content: "";
            clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
            border-radius: 2px;
        }

        &::after {
            background: linear-gradient(to left, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1));
            opacity: 0;
            visibility: hidden;
        }

        &:hover::after {
            opacity: 1;
            visibility: visible;
        }

        .inner {
            display: inline-flex;
            position: relative;
            z-index: 3;
            background: var(--td-void);
            clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
            gap: 12px;
            width: 100%;

            &::before {
                position: absolute;
                top: 0;
                inset-inline-start: 0;
                content: "";
                clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
                z-index: -1;
                width: 100%;
                height: 100%;
            }
        }

        .filter-bar-search {
            .input-box {
                border: 0;
            }
        }

    }

    .input-box {
        background-color: transparent;
        padding: 0px 16px 0px 16px;
        border: 1px solid rgba($heading, $alpha: 0.1);
        height: 44px;
        color: var(--td-text-primary);
        font-weight: 500;
        font-size: 14px;

        @include dark-theme {
            border-color: rgba($white, $alpha: 0.1);
            color: var(--td-text-secondary);
        }

        @include td-placeholder {
            color: var(--td-text-primary);
            font-weight: 500;
            font-size: 14px;

            @include dark-theme {
                color: var(--td-text-secondary);
            }
        }
    }

    .filter-bar-search {
        position: relative;
        width: 100%;

        .input-box {
            padding-inline-start: 40px;
        }

        .search-icon {
            position: absolute;
            top: 50%;
            inset-inline-start: 16px;
            transform: translateY(-50%);

            svg *,
            i {
                stroke: var(--td-text-primary);
                color: var(--td-text-primary);

                @include dark-theme {
                    stroke: var(--td-text-secondary);
                    color: var(--td-text-secondary);
                }
            }
        }
    }

    .td-form-group {
        &.has-right-icon .input-icon i {
            width: 18px;
            height: 18px;
        }

        .input-field {
            .select2-container--default .select2-selection--single {
                height: 44px;
                line-height: 44px;
            }

            input {
                background-color: transparent;
                padding: 0px 16px 0px 16px;
                height: 44px;
                border: 1px solid rgba($heading, $alpha: 0.16);
                font-weight: 500;
                font-size: 14px;

                @include dark-theme {
                    border-color: rgba($white, $alpha: 0.1);
                    color: var(--td-text-secondary);

                    @include td-placeholder {
                        color: var(--td-text-secondary);
                    }
                }
            }
        }
    }

    .filter-item {
        .td-btn {
            @media #{$xs} {
                width: 100%;
            }
        }
    }
}

// Custom table
.td-custom-table {
    .clip-path {
        position: relative;
        padding: 1px;

        .inner {
            display: flex;
            position: relative;
            z-index: 3;
            background: var(--td-void);
            clip-path: polygon(5.116% 0.575%, 98.367% 0.575%, 98.367% 0.575%, 98.381% 0.579%, 98.395% 0.593%, 98.409% 0.614%, 98.423% 0.644%, 98.436% 0.682%, 98.449% 0.728%, 98.462% 0.782%, 98.474% 0.844%, 98.486% 0.913%, 98.497% 0.99%, 98.512% 1.112%, 99.873% 13.129%, 99.873% 13.129%, 99.885% 13.24%, 99.895% 13.358%, 99.905% 13.483%, 99.913% 13.614%, 99.92% 13.75%, 99.926% 13.891%, 99.931% 14.036%, 99.934% 14.184%, 99.936% 14.334%, 99.937% 14.487%, 99.937% 65.391%, 99.937% 65.391%, 99.936% 65.532%, 99.934% 65.671%, 99.931% 65.807%, 99.928% 65.941%, 99.923% 66.072%, 99.917% 66.199%, 99.91% 66.322%, 99.902% 66.44%, 99.893% 66.552%, 99.883% 66.659%, 99.867% 66.802%, 95.926% 98.823%, 95.926% 98.823%, 95.914% 98.912%, 95.902% 98.993%, 95.889% 99.065%, 95.875% 99.127%, 95.861% 99.181%, 95.847% 99.225%, 95.832% 99.26%, 95.817% 99.285%, 95.802% 99.3%, 95.787% 99.306%, 0.272% 99.306%, 0.272% 99.306%, 0.238% 99.281%, 0.206% 99.209%, 0.176% 99.094%, 0.149% 98.94%, 0.125% 98.751%, 0.104% 98.531%, 0.087% 98.283%, 0.074% 98.011%, 0.066% 97.72%, 0.063% 97.413%, 0.063% 41.89%, 0.063% 41.89%, 0.064% 41.749%, 0.066% 41.61%, 0.068% 41.474%, 0.072% 41.34%, 0.077% 41.209%, 0.083% 41.082%, 0.09% 40.959%, 0.098% 40.841%, 0.107% 40.728%, 0.117% 40.621%, 0.133% 40.479%, 4.976% 1.06%, 4.976% 1.06%, 4.988% 0.97%, 5.001% 0.89%, 5.014% 0.818%, 5.027% 0.755%, 5.041% 0.701%, 5.055% 0.656%, 5.07% 0.621%, 5.085% 0.596%, 5.1% 0.58%, 5.116% 0.575%);
            gap: 12px;
            padding: 2px 40px;

            &::before {
                position: absolute;
                top: 0;
                inset-inline-start: 0;
                content: "";
                background: linear-gradient(90deg, rgba(71, 118, 230, 0.2) 68.94%, rgba(142, 84, 233, 0.2) 100%);
                z-index: -1;
                width: 100%;
                height: 100%;
            }
        }

        &::before {
            position: absolute;
            inset-inline-start: 0;
            top: 0;
            transition: all 0.3s;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, rgba(71, 118, 230, 0.2) 68.94%, rgba(142, 84, 233, 0.2) 100%);
            opacity: 1;
            content: "";
            clip-path: polygon(5.116% 0.575%, 98.367% 0.575%, 98.367% 0.575%, 98.381% 0.579%, 98.395% 0.593%, 98.409% 114%, 98.423% 0.644%, 98.436% 0.682%, 98.449% 0.728%, 98.462% 0.782%, 98.474% 0.844%, 98.486% 0.913%, 98.497% 0.99%, 98.512% 1.112%, 99.873% 13.129%, 99.873% 13.129%, 99.885% 13.24%, 99.895% 13.358%, 99.905% 13.483%, 99.913% 13.614%, 99.92% 13.75%, 99.926% 13.891%, 99.931% 14.036%, 99.934% 14.184%, 99.936% 14.334%, 99.937% 14.487%, 99.937% 65.391%, 99.937% 65.391%, 99.936% 65.532%, 99.934% 65.671%, 99.931% 65.807%, 99.928% 65.941%, 99.923% 66.072%, 99.917% 66.199%, 99.91% 66.322%, 99.902% 66.44%, 99.893% 66.552%, 99.883% 66.659%, 99.867% 66.802%, 95.926% 98.823%, 95.926% 98.823%, 95.914% 98.912%, 95.902% 98.993%, 95.889% 99.065%, 95.875% 99.127%, 95.861% 99.181%, 95.847% 99.225%, 95.832% 99.26%, 95.817% 99.285%, 95.802% 99.3%, 95.787% 99.306%, 0.272% 99.306%, 0.272% 99.306%, 0.238% 99.281%, 0.206% 99.209%, 0.176% 99.094%, 0.149% 98.94%, 0.125% 98.751%, 0.104% 98.531%, 0.087% 98.283%, 0.074% 98.011%, 0.066% 97.72%, 0.063% 97.413%, 0.063% 41.89%, 0.063% 41.89%, 0.064% 41.749%, 0.066% 41.61%, 0.068% 41.474%, 0.072% 41.34%, 0.077% 41.209%, 0.083% 41.082%, 0.09% 40.959%, 0.098% 40.841%, 0.107% 40.728%, 0.117% 40.621%, 0.133% 40.479%, 4.976% 1.06%, 4.976% 1.06%, 4.988% 0.97%, 5.001% 0.89%, 5.014% 0.818%, 5.027% 0.755%, 5.041% 0.701%, 5.055% 0.656%, 5.07% 0.621%, 5.085% 0.596%, 5.1% 0.58%, 5.116% 0.575%);
            border-radius: 2px;
        }

        &::after {
            position: absolute;
            inset-inline-start: 0;
            top: 0;
            transition: all 0.3s;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, rgba(71, 118, 230, 0.2) 68.94%, rgba(142, 84, 233, 0.2) 100%);
            opacity: 1;
            content: "";
            clip-path: polygon(5.116% 0.575%, 98.367% 0.575%, 98.367% 0.575%, 98.381% 0.579%, 98.395% 0.593%, 98.409% 114%, 98.423% 0.644%, 98.436% 0.682%, 98.449% 0.728%, 98.462% 0.782%, 98.474% 0.844%, 98.486% 0.913%, 98.497% 0.99%, 98.512% 1.112%, 99.873% 13.129%, 99.873% 13.129%, 99.885% 13.24%, 99.895% 13.358%, 99.905% 13.483%, 99.913% 13.614%, 99.92% 13.75%, 99.926% 13.891%, 99.931% 14.036%, 99.934% 14.184%, 99.936% 14.334%, 99.937% 14.487%, 99.937% 65.391%, 99.937% 65.391%, 99.936% 65.532%, 99.934% 65.671%, 99.931% 65.807%, 99.928% 65.941%, 99.923% 66.072%, 99.917% 66.199%, 99.91% 66.322%, 99.902% 66.44%, 99.893% 66.552%, 99.883% 66.659%, 99.867% 66.802%, 95.926% 98.823%, 95.926% 98.823%, 95.914% 98.912%, 95.902% 98.993%, 95.889% 99.065%, 95.875% 99.127%, 95.861% 99.181%, 95.847% 99.225%, 95.832% 99.26%, 95.817% 99.285%, 95.802% 99.3%, 95.787% 99.306%, 0.272% 99.306%, 0.272% 99.306%, 0.238% 99.281%, 0.206% 99.209%, 0.176% 99.094%, 0.149% 98.94%, 0.125% 98.751%, 0.104% 98.531%, 0.087% 98.283%, 0.074% 98.011%, 0.066% 97.72%, 0.063% 97.413%, 0.063% 41.89%, 0.063% 41.89%, 0.064% 41.749%, 0.066% 41.61%, 0.068% 41.474%, 0.072% 41.34%, 0.077% 41.209%, 0.083% 41.082%, 0.09% 40.959%, 0.098% 40.841%, 0.107% 40.728%, 0.117% 40.621%, 0.133% 40.479%, 4.976% 1.06%, 4.976% 1.06%, 4.988% 0.97%, 5.001% 0.89%, 5.014% 0.818%, 5.027% 0.755%, 5.041% 0.701%, 5.055% 0.656%, 5.07% 0.621%, 5.085% 0.596%, 5.1% 0.58%, 5.116% 0.575%);
            border-radius: 2px;
        }
    }

    .contents {
        display: table;
        width: 100%;
        font-size: 14px;

        .site-table-list {
            display: table-row;

            .site-table-col {
                position: relative;
                display: table-cell;
                vertical-align: middle;
                padding: 20px 16px;
                padding-inline-start: 20px;

                @include dark-theme {
                    color: var(--td-white);
                }

                &:last-child {
                    text-align: end;
                }

            }

            &:last-child {
                .site-table-col {
                    border-bottom: 0;
                }
            }
        }

        .site-table-head {
            .site-table-col {
                padding-top: 14px;
                padding-bottom: 14px;
                font-size: 14px;
                font-weight: 700;
                color: var(--td-heading);

                @include dark-theme {
                    color: var(--td-white);
                }
            }
        }
    }

    .right-two-grid {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 20px;
        margin-inline-start: auto;
        width: 75%;

        * {
            flex: 0 0 auto;
        }
    }
}

.site-table-list.site-table-head {
    position: relative;
    background-size: 100% 100%;
    background-repeat: no-repeat;
}