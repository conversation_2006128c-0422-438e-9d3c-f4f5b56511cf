@use '../utils' as *;

/*----------------------------------------*/
/*  Select2 customize
/*----------------------------------------*/
.select2-dropdown {
	background: #fff;
	box-shadow: none;

	@include dark-theme {
		background-color: #17213f;
	}

	&.select2-dropdown--above {
		box-shadow: none !important;
		border: 1px solid rgba(8, 8, 8, 0.16) !important;

		@include dark-theme {
			border-color: rgba(255, 255, 255, 0.1) !important;
		}
	}
}

.select2-container--default {
	.select2-results__option--highlighted[aria-selected] {
		background-color: var(--td-primary) !important;
		color: var(--td-white) !important;
	}

	.select2-selection {
		background-color: transparent;
		transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
		border: 1px solid rgba(8, 8, 8, 0.16);
		border-radius: 30px;

		@include dark-theme {
			background-color: rgba($white, $alpha: 0.04);
			border: 1px solid rgba($white, $alpha: 0.1);
		}

		&:hover {
			border-color: rgba(8, 8, 8, 0.16) !important;

			@include dark-theme {
				border-color: rgba(255, 255, 255, 0.1) !important;
			}
		}
	}

	.select2-selection--single {
		height: 50px;
		line-height: 50px;
		display: flex;
		align-items: center;

		.select2-selection__arrow {
			top: 6px;
			width: 20px !important;
			height: 100% !important;
			display: inline-flex;
			align-items: center;
			justify-content: center;
			top: 0;
			inset-inline-end: 6px;

			b {
				position: absolute;
				background-repeat: no-repeat;
				background-size: 20px 19px;
				transform-origin: center;
				transition: transform 0.3s ease;
				width: 8px;
				height: 8px;
				border-bottom: 1.5px solid var(--td-text-primary);
				border-inline-end: 1.5px solid var(--td-text-primary);
				position: absolute;
				top: 45%;
				transform: translateY(-50%) rotate(45deg);

				@include rtl {
					transform: translateY(-50%) rotate(320deg)
				}
			}
		}

		.select2-selection__rendered {
			color: var(--td-text-primary);
			font-weight: 600;
			font-size: 14px;
			padding-left: 16px !important;

			@include dark-theme {
				color: var(--td-text-secondary);
			}
		}
	}

	.select2-search--dropdown {
		.select2-search__field {
			height: 40px;
			background-color: transparent;
			border-color: rgba(8, 8, 8, 0.16);

			@include dark-theme {
				border-color: rgba(255, 255, 255, 0.1) !important;
				color: var(--td-text-secondary);
			}

			padding: 0 12px;
		}
	}

	&.select2-container--focus {
		.select2-selection {
			border-color: rgba($primary, $alpha: 0.5) !important;
		}
	}

	&.select2-container--open {
		.select2-selection {
			border-color: rgba($primary, $alpha: 0.5) !important;
		}
	}

	.select2-results__option {
		color: var(--td-text-primary);
		font-weight: 600;
		font-size: 14px;

		@include dark-theme {
			color: var(--td-text-secondary);
		}
	}

	&.select2-container--above {
		&.select2-container--open {
			.select2-selection__arrow {
				b {
					transform: rotate(222deg) !important;

					@include rtl {
						transform: rotate(135deg) !important;
					}
				}
			}
		}
	}
}

.select2-primary {
	.select2-container--default {
		.select2-selection--multiple {
			.select2-selection__choice {
				background: rgba(115, 103, 240, 0.16) !important;
				color: var(--td-primary) !important;
			}
		}
	}
}

.select2-search__field {
	height: 40px;
}

.defaults-select {
	.select2-container--default {
		&.select2-container--focus {
			.select2-selection {
				border-width: 0;
			}
		}

		.select2-selection {
			border: 0;
		}

		.select2-selection--single {
			height: inherit;

			.select2-selection__arrow {
				top: 0px;

				b {
					background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 22" fill="none"><path d="M10.9999 12.0743L15.5374 7.53676L16.8336 8.83292L10.9999 14.6666L5.16626 8.83292L6.46243 7.53676L10.9999 12.0743Z" fill="%232f2b3d" fill-opacity="0.9"/></svg>');
					background-size: 20px;
					inset-inline-end: 10px;
				}
			}
		}
	}

	.select2-dropdown {
		min-width: 200px;
	}

	.select2-results__options {
		&::-webkit-scrollbar {
			width: 5px;
		}

		&::-webkit-scrollbar-thumb {
			background: rgba($white, $alpha: 0.2);
			border-radius: 10px;
		}
	}
}

.select2-results__options {
	&::-webkit-scrollbar {
		width: 6px;
		background-clip: padding-box;
	}

	&::-webkit-scrollbar-track {
		background-color: transparent;
		height: 8px;
		background-clip: padding-box;
		border-inline-end: 10px solid rgba(0, 0, 0, 0);
		border-top: 10px solid rgba(0, 0, 0, 0);
		border-bottom: 10px solid rgba(0, 0, 0, 0);
		border-radius: 20px;
	}

	&::-webkit-scrollbar-thumb {
		background-clip: padding-box;
		background-color: #d9d9d9;
		border-inline-end: 10px solid rgba(0, 0, 0, 0);
		border-top: 10px solid rgba(0, 0, 0, 0);
		border-bottom: 10px solid rgba(0, 0, 0, 0);
		border-radius: 20px;

		@include dark-theme {
			background-color: #2C2F3B;
		}
	}
}

.select2-container--open {
	.select2-dropdown--below {
		border: 1px solid rgba(8, 8, 8, 0.16);

		@include dark-theme {
			border-color: rgba(255, 255, 255, 0.1) !important;
		}
	}
}