1750325544O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:25:{i:0;O:26:"App\Models\LoginActivities":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"login_activities";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:1;s:7:"user_id";i:2;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-21 12:23:11";s:10:"updated_at";s:19:"2025-05-21 12:23:11";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:1;s:7:"user_id";i:2;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-21 12:23:11";s:10:"updated_at";s:19:"2025-05-21 12:23:11";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:7:"browser";i:1;s:8:"platform";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:16:" * forceDeleting";b:0;}i:1;O:26:"App\Models\LoginActivities":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"login_activities";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:2;s:7:"user_id";i:3;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-21 12:25:20";s:10:"updated_at";s:19:"2025-05-21 12:25:20";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:2;s:7:"user_id";i:3;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-21 12:25:20";s:10:"updated_at";s:19:"2025-05-21 12:25:20";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:7:"browser";i:1;s:8:"platform";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:16:" * forceDeleting";b:0;}i:2;O:26:"App\Models\LoginActivities":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"login_activities";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:3;s:7:"user_id";i:4;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-21 12:26:13";s:10:"updated_at";s:19:"2025-05-21 12:26:13";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:3;s:7:"user_id";i:4;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-21 12:26:13";s:10:"updated_at";s:19:"2025-05-21 12:26:13";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:7:"browser";i:1;s:8:"platform";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:16:" * forceDeleting";b:0;}i:3;O:26:"App\Models\LoginActivities":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"login_activities";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:4;s:7:"user_id";i:5;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-21 12:28:16";s:10:"updated_at";s:19:"2025-05-21 12:28:16";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:4;s:7:"user_id";i:5;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-21 12:28:16";s:10:"updated_at";s:19:"2025-05-21 12:28:16";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:7:"browser";i:1;s:8:"platform";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:16:" * forceDeleting";b:0;}i:4;O:26:"App\Models\LoginActivities":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"login_activities";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:5;s:7:"user_id";i:8;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-21 12:29:25";s:10:"updated_at";s:19:"2025-05-21 12:29:25";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:5;s:7:"user_id";i:8;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-21 12:29:25";s:10:"updated_at";s:19:"2025-05-21 12:29:25";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:7:"browser";i:1;s:8:"platform";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:16:" * forceDeleting";b:0;}i:5;O:26:"App\Models\LoginActivities":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"login_activities";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:6;s:7:"user_id";i:9;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-21 12:29:49";s:10:"updated_at";s:19:"2025-05-21 12:29:49";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:6;s:7:"user_id";i:9;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-21 12:29:49";s:10:"updated_at";s:19:"2025-05-21 12:29:49";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:7:"browser";i:1;s:8:"platform";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:16:" * forceDeleting";b:0;}i:6;O:26:"App\Models\LoginActivities":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"login_activities";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:7;s:7:"user_id";i:10;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-21 12:30:49";s:10:"updated_at";s:19:"2025-05-21 12:30:49";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:7;s:7:"user_id";i:10;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-21 12:30:49";s:10:"updated_at";s:19:"2025-05-21 12:30:49";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:7:"browser";i:1;s:8:"platform";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:16:" * forceDeleting";b:0;}i:7;O:26:"App\Models\LoginActivities":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"login_activities";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:8;s:7:"user_id";i:11;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-21 12:31:10";s:10:"updated_at";s:19:"2025-05-21 12:31:10";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:8;s:7:"user_id";i:11;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-21 12:31:10";s:10:"updated_at";s:19:"2025-05-21 12:31:10";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:7:"browser";i:1;s:8:"platform";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:16:" * forceDeleting";b:0;}i:8;O:26:"App\Models\LoginActivities":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"login_activities";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:9;s:7:"user_id";i:12;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:125:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********";s:10:"created_at";s:19:"2025-05-21 15:23:28";s:10:"updated_at";s:19:"2025-05-21 15:23:28";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:9;s:7:"user_id";i:12;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:125:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********";s:10:"created_at";s:19:"2025-05-21 15:23:28";s:10:"updated_at";s:19:"2025-05-21 15:23:28";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:7:"browser";i:1;s:8:"platform";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:16:" * forceDeleting";b:0;}i:9;O:26:"App\Models\LoginActivities":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"login_activities";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:10;s:7:"user_id";i:13;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:125:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********";s:10:"created_at";s:19:"2025-05-21 15:25:23";s:10:"updated_at";s:19:"2025-05-21 15:25:23";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:10;s:7:"user_id";i:13;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:125:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********";s:10:"created_at";s:19:"2025-05-21 15:25:23";s:10:"updated_at";s:19:"2025-05-21 15:25:23";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:7:"browser";i:1;s:8:"platform";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:16:" * forceDeleting";b:0;}i:10;O:26:"App\Models\LoginActivities":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"login_activities";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:11;s:7:"user_id";i:14;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:125:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********";s:10:"created_at";s:19:"2025-05-21 15:27:05";s:10:"updated_at";s:19:"2025-05-21 15:27:05";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:11;s:7:"user_id";i:14;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:125:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********";s:10:"created_at";s:19:"2025-05-21 15:27:05";s:10:"updated_at";s:19:"2025-05-21 15:27:05";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:7:"browser";i:1;s:8:"platform";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:16:" * forceDeleting";b:0;}i:11;O:26:"App\Models\LoginActivities":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"login_activities";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:12;s:7:"user_id";i:15;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:125:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********";s:10:"created_at";s:19:"2025-05-21 15:28:03";s:10:"updated_at";s:19:"2025-05-21 15:28:03";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:12;s:7:"user_id";i:15;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:125:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********";s:10:"created_at";s:19:"2025-05-21 15:28:03";s:10:"updated_at";s:19:"2025-05-21 15:28:03";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:7:"browser";i:1;s:8:"platform";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:16:" * forceDeleting";b:0;}i:12;O:26:"App\Models\LoginActivities":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"login_activities";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:13;s:7:"user_id";i:23;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:125:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********";s:10:"created_at";s:19:"2025-05-21 15:32:40";s:10:"updated_at";s:19:"2025-05-21 15:32:40";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:13;s:7:"user_id";i:23;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:125:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********";s:10:"created_at";s:19:"2025-05-21 15:32:40";s:10:"updated_at";s:19:"2025-05-21 15:32:40";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:7:"browser";i:1;s:8:"platform";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:16:" * forceDeleting";b:0;}i:13;O:26:"App\Models\LoginActivities":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"login_activities";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:14;s:7:"user_id";i:28;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:125:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********";s:10:"created_at";s:19:"2025-05-21 15:45:30";s:10:"updated_at";s:19:"2025-05-21 15:45:30";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:14;s:7:"user_id";i:28;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:125:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********";s:10:"created_at";s:19:"2025-05-21 15:45:30";s:10:"updated_at";s:19:"2025-05-21 15:45:30";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:7:"browser";i:1;s:8:"platform";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:16:" * forceDeleting";b:0;}i:14;O:26:"App\Models\LoginActivities":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"login_activities";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:15;s:7:"user_id";i:36;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:125:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********";s:10:"created_at";s:19:"2025-05-21 15:53:34";s:10:"updated_at";s:19:"2025-05-21 15:53:34";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:15;s:7:"user_id";i:36;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:125:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********";s:10:"created_at";s:19:"2025-05-21 15:53:34";s:10:"updated_at";s:19:"2025-05-21 15:53:34";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:7:"browser";i:1;s:8:"platform";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:16:" * forceDeleting";b:0;}i:15;O:26:"App\Models\LoginActivities":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"login_activities";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:16;s:7:"user_id";i:37;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:125:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********";s:10:"created_at";s:19:"2025-05-25 09:51:03";s:10:"updated_at";s:19:"2025-05-25 09:51:03";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:16;s:7:"user_id";i:37;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:125:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********";s:10:"created_at";s:19:"2025-05-25 09:51:03";s:10:"updated_at";s:19:"2025-05-25 09:51:03";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:7:"browser";i:1;s:8:"platform";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:16:" * forceDeleting";b:0;}i:16;O:26:"App\Models\LoginActivities":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"login_activities";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:17;s:7:"user_id";i:38;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:125:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********";s:10:"created_at";s:19:"2025-05-25 09:52:12";s:10:"updated_at";s:19:"2025-05-25 09:52:12";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:17;s:7:"user_id";i:38;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:125:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********";s:10:"created_at";s:19:"2025-05-25 09:52:12";s:10:"updated_at";s:19:"2025-05-25 09:52:12";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:7:"browser";i:1;s:8:"platform";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:16:" * forceDeleting";b:0;}i:17;O:26:"App\Models\LoginActivities":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"login_activities";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:18;s:7:"user_id";i:39;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-26 14:52:36";s:10:"updated_at";s:19:"2025-05-26 14:52:36";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:18;s:7:"user_id";i:39;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-26 14:52:36";s:10:"updated_at";s:19:"2025-05-26 14:52:36";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:7:"browser";i:1;s:8:"platform";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:16:" * forceDeleting";b:0;}i:18;O:26:"App\Models\LoginActivities":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"login_activities";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:19;s:7:"user_id";i:1;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-26 15:11:09";s:10:"updated_at";s:19:"2025-05-26 15:11:09";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:19;s:7:"user_id";i:1;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-26 15:11:09";s:10:"updated_at";s:19:"2025-05-26 15:11:09";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:7:"browser";i:1;s:8:"platform";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:16:" * forceDeleting";b:0;}i:19;O:26:"App\Models\LoginActivities":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"login_activities";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:20;s:7:"user_id";i:1;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-26 15:11:21";s:10:"updated_at";s:19:"2025-05-26 15:11:21";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:20;s:7:"user_id";i:1;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-26 15:11:21";s:10:"updated_at";s:19:"2025-05-26 15:11:21";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:7:"browser";i:1;s:8:"platform";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:16:" * forceDeleting";b:0;}i:20;O:26:"App\Models\LoginActivities":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"login_activities";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:21;s:7:"user_id";i:41;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-31 10:07:32";s:10:"updated_at";s:19:"2025-05-31 10:07:32";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:21;s:7:"user_id";i:41;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-31 10:07:32";s:10:"updated_at";s:19:"2025-05-31 10:07:32";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:7:"browser";i:1;s:8:"platform";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:16:" * forceDeleting";b:0;}i:21;O:26:"App\Models\LoginActivities":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"login_activities";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:22;s:7:"user_id";i:42;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-31 10:08:16";s:10:"updated_at";s:19:"2025-05-31 10:08:16";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:22;s:7:"user_id";i:42;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-31 10:08:16";s:10:"updated_at";s:19:"2025-05-31 10:08:16";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:7:"browser";i:1;s:8:"platform";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:16:" * forceDeleting";b:0;}i:22;O:26:"App\Models\LoginActivities":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"login_activities";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:23;s:7:"user_id";i:43;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-31 10:15:59";s:10:"updated_at";s:19:"2025-05-31 10:15:59";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:23;s:7:"user_id";i:43;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-05-31 10:15:59";s:10:"updated_at";s:19:"2025-05-31 10:15:59";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:7:"browser";i:1;s:8:"platform";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:16:" * forceDeleting";b:0;}i:23;O:26:"App\Models\LoginActivities":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"login_activities";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:24;s:7:"user_id";i:44;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:125:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********";s:10:"created_at";s:19:"2025-05-31 12:55:21";s:10:"updated_at";s:19:"2025-05-31 12:55:21";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:24;s:7:"user_id";i:44;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:125:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********";s:10:"created_at";s:19:"2025-05-31 12:55:21";s:10:"updated_at";s:19:"2025-05-31 12:55:21";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:7:"browser";i:1;s:8:"platform";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:16:" * forceDeleting";b:0;}i:24;O:26:"App\Models\LoginActivities":34:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"login_activities";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:25;s:7:"user_id";i:45;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-06-02 10:27:19";s:10:"updated_at";s:19:"2025-06-02 10:27:19";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:25;s:7:"user_id";i:45;s:2:"ip";s:9:"127.0.0.1";s:8:"location";s:10:"Bangladesh";s:5:"agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:10:"created_at";s:19:"2025-06-02 10:27:19";s:10:"updated_at";s:19:"2025-06-02 10:27:19";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:7:"browser";i:1;s:8:"platform";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:16:" * forceDeleting";b:0;}}s:28:" * escapeWhenCastingToString";b:0;}