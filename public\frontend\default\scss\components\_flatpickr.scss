@use "../utils" as *;

/*----------------------------------------*/
/* Date range picker
/*----------------------------------------*/
.flatpickr-months .flatpickr-prev-month:hover svg,
.flatpickr-months .flatpickr-next-month:hover svg {
    fill: var(--td-primary);
}

.flatpickr-current-month,
.flatpickr-current-month .flatpickr-monthDropdown-months,
.flatpickr-calendar {
    font-family: var(--td-ff-body) !important;
    font-size: 16px !important;

    option {
        font-family: var(--td-ff-body) !important;
        font-size: 18px !important;
    }
}

.flatpickr-calendar {
    @include dark-theme {
        background: #3f4458 !important;
        -webkit-box-shadow: 1px 0 0 #20222c, -1px 0 0 #20222c, 0 1px 0 #20222c, 0 -1px 0 #20222c, 0 3px 13px rgba(0, 0, 0, 0.08);
        box-shadow: 1px 0 0 #20222c, -1px 0 0 #20222c, 0 1px 0 #20222c, 0 -1px 0 #20222c, 0 3px 13px rgba(0, 0, 0, 0.08);
    }
}

.flatpickr-current-month .flatpickr-monthDropdown-months {
    @include dark-theme {
        background: #3f4458 !important;
    }
}

.flatpickr-weekday {
    @include dark-theme {
        background: #3f4458 !important;
        color: #fff !important;
    }
}

.flatpickr-calendar.arrowTop:after {
    @include dark-theme {
        border-bottom-color: #3f4458;
    }
}

.flatpickr-calendar.arrowTop:before {
    @include dark-theme {
        border-bottom-color: #3f4458;
    }
}

.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
    @include dark-theme {
        color: #fff;
        fill: #fff;
    }
}

.flatpickr-day.inRange,
.flatpickr-day.prevMonthDay.inRange,
.flatpickr-day.nextMonthDay.inRange,
.flatpickr-day.today.inRange,
.flatpickr-day.prevMonthDay.today.inRange,
.flatpickr-day.nextMonthDay.today.inRange,
.flatpickr-day:hover,
.flatpickr-day.prevMonthDay:hover,
.flatpickr-day.nextMonthDay:hover,
.flatpickr-day:focus,
.flatpickr-day.prevMonthDay:focus,
.flatpickr-day.nextMonthDay:focus {

    @include dark-theme {
        background: #646c8c;
        border-color: #646c8c;
    }
}

.flatpickr-day {
    @include dark-theme {
        color: rgba(255, 255, 255, 0.95)
    }
}

.numInputWrapper span {
    @include dark-theme {
        border: 1px solid rgba(255, 255, 255, 0.15);
    }
}

.flatpickr-months .flatpickr-month {
    @include dark-theme {
        color: #fff;
        fill: #fff;
    }
}

.flatpickr-current-month .numInputWrapper span.arrowUp:after {
    @include dark-theme {
        border-bottom-color: #fff;
    }
}

.flatpickr-current-month .numInputWrapper span.arrowDown:after {
    @include dark-theme {
        border-top-color: #fff;
    }
}

.flatpickr-day.inRange {
    @include dark-theme {
        -webkit-box-shadow: -5px 0 0 #646c8c, 5px 0 0 #646c8c;
        box-shadow: -5px 0 0 #646c8c, 5px 0 0 #646c8c;
    }
}

.flatpickr-day.flatpickr-disabled,
.flatpickr-day.flatpickr-disabled:hover,
.flatpickr-day.prevMonthDay,
.flatpickr-day.nextMonthDay,
.flatpickr-day.notAllowed,
.flatpickr-day.notAllowed.prevMonthDay,
.flatpickr-day.notAllowed.nextMonthDay {
    @include dark-theme {
        color: rgba(255, 255, 255, 0.3);
        background: transparent;
        border-color: transparent;
    }
}