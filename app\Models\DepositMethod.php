<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DepositMethod extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $appends = [
        'gateway_logo',
        'currency_decimals',
    ];

    protected function currencyDecimals(): Attribute
    {
        return Attribute::make(get: function () {
            return $this->currency != setting('site_currency', 'global') ? 8 : setting('site_currency_decimals', 'global');
        });
    }

    public function gateway(): BelongsTo
    {
        return $this->belongsTo(Gateway::class, 'gateway_id');
    }

    public function coin()
    {
        return $this->belongsTo(Coin::class, 'currency', 'code');
    }

    public function scopeCode($query, $code)
    {
        return $query->where('gateway_code', $code);
    }

    protected function gatewayLogo(): Attribute
    {
        return Attribute::make(get: function () {
            if ($this->logo == null) {
                return asset($this->gateway?->logo);
            }

            return asset($this->logo);
        });
    }

    protected function casts(): array
    {
        return [
            'field_options' => 'json',
        ];
    }

    /**
     * Get the charge amount
     *
     * @return array [payAmount, charge, finalAmount]
     */
    public function getChargeAmount(float $amount = 0): array
    {
        $finalPrice = $amount;
        $charge = (float) $this->charge_type == 'percentage' ? (($this->charge / 100) * $finalPrice) : $this->charge;

        $coinRate = setting('site_currency', 'global') == $this->currency ? 1 : $this->coin?->conversion_rate ?? 1;
        $finalAmount = (float) $finalPrice;
        
        $payAmount = ($finalAmount + $charge) * $coinRate;
        $charge = $this->charge_type != 'percentage' ? $charge : $charge * $coinRate;

        return [$payAmount, $charge, $finalAmount];
    }
}
