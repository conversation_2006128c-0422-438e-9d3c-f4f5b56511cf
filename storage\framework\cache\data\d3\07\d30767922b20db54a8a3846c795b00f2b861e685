9999999999O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:170:{i:0;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:1;s:4:"name";s:9:"site_logo";s:3:"val";s:64:"global/uploads/global/uploads/settings//dMZgQ9AsyaYuZgwiCa16.png";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-19 16:22:00";}s:11:" * original";a:6:{s:2:"id";i:1;s:4:"name";s:9:"site_logo";s:3:"val";s:64:"global/uploads/global/uploads/settings//dMZgQ9AsyaYuZgwiCa16.png";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-19 16:22:00";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:1;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:2;s:4:"name";s:16:"site_logo_height";s:3:"val";s:4:"35px";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 10:58:29";}s:11:" * original";a:6:{s:2:"id";i:2;s:4:"name";s:16:"site_logo_height";s:3:"val";s:4:"35px";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 10:58:29";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:2;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:3;s:4:"name";s:15:"site_logo_width";s:3:"val";s:4:"35px";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 10:58:29";}s:11:" * original";a:6:{s:2:"id";i:3;s:4:"name";s:15:"site_logo_width";s:3:"val";s:4:"35px";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 10:58:29";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:3;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:4;s:4:"name";s:12:"site_favicon";s:3:"val";s:64:"global/uploads/global/uploads/settings//r5688ZF7d9Jery0sTIxk.png";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-19 16:22:00";}s:11:" * original";a:6:{s:2:"id";i:4;s:4:"name";s:12:"site_favicon";s:3:"val";s:64:"global/uploads/global/uploads/settings//r5688ZF7d9Jery0sTIxk.png";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-19 16:22:00";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:4;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:5;s:4:"name";s:8:"login_bg";s:3:"val";s:64:"global/uploads/global/uploads/settings//aAD3se0DN8DVckXkGBEk.jpg";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-03-24 08:36:51";}s:11:" * original";a:6:{s:2:"id";i:5;s:4:"name";s:8:"login_bg";s:3:"val";s:64:"global/uploads/global/uploads/settings//aAD3se0DN8DVckXkGBEk.jpg";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-03-24 08:36:51";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:5;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:6;s:4:"name";s:17:"site_admin_prefix";s:3:"val";s:5:"admin";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-04-16 17:58:11";}s:11:" * original";a:6:{s:2:"id";i:6;s:4:"name";s:17:"site_admin_prefix";s:3:"val";s:5:"admin";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-04-16 17:58:11";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:6;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:7;s:4:"name";s:10:"site_title";s:3:"val";s:8:"OrexCoin";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-04 10:43:41";}s:11:" * original";a:6:{s:2:"id";i:7;s:4:"name";s:10:"site_title";s:3:"val";s:8:"OrexCoin";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-04 10:43:41";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:7;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:8;s:4:"name";s:21:"account_number_prefix";s:3:"val";s:3:"DGB";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 10:58:29";}s:11:" * original";a:6:{s:2:"id";i:8;s:4:"name";s:21:"account_number_prefix";s:3:"val";s:3:"DGB";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 10:58:29";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:8;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:9;s:4:"name";s:13:"site_currency";s:3:"val";s:3:"USD";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 10:58:29";}s:11:" * original";a:6:{s:2:"id";i:9;s:4:"name";s:13:"site_currency";s:3:"val";s:3:"USD";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 10:58:29";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:9;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:10;s:4:"name";s:15:"currency_symbol";s:3:"val";s:1:"$";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 10:58:29";}s:11:" * original";a:6:{s:2:"id";i:10;s:4:"name";s:15:"currency_symbol";s:3:"val";s:1:"$";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 10:58:29";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:10;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:11;s:4:"name";s:13:"site_timezone";s:3:"val";s:3:"UTC";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-04-26 16:10:51";}s:11:" * original";a:6:{s:2:"id";i:11;s:4:"name";s:13:"site_timezone";s:3:"val";s:3:"UTC";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-04-26 16:10:51";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:11;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:12;s:4:"name";s:19:"referral_code_limit";s:3:"val";s:1:"6";s:4:"type";s:7:"integer";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 10:58:29";}s:11:" * original";a:6:{s:2:"id";i:12;s:4:"name";s:19:"referral_code_limit";s:3:"val";s:1:"6";s:4:"type";s:7:"integer";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 10:58:29";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:12;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:13;s:4:"name";s:16:"account_no_limit";s:3:"val";s:2:"10";s:4:"type";s:7:"integer";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 10:58:29";}s:11:" * original";a:6:{s:2:"id";i:13;s:4:"name";s:16:"account_no_limit";s:3:"val";s:2:"10";s:4:"type";s:7:"integer";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 10:58:29";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:13;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:14;s:4:"name";s:13:"home_redirect";s:3:"val";s:1:"/";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-02 15:05:36";}s:11:" * original";a:6:{s:2:"id";i:14;s:4:"name";s:13:"home_redirect";s:3:"val";s:1:"/";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-02 15:05:36";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:14;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:15;s:4:"name";s:10:"site_email";s:3:"val";s:14:"<EMAIL>";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 10:58:29";}s:11:" * original";a:6:{s:2:"id";i:15;s:4:"name";s:10:"site_email";s:3:"val";s:14:"<EMAIL>";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 10:58:29";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:15;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:16;s:4:"name";s:13:"support_email";s:3:"val";s:16:"<EMAIL>";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 10:58:29";}s:11:" * original";a:6:{s:2:"id";i:16;s:4:"name";s:13:"support_email";s:3:"val";s:16:"<EMAIL>";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 10:58:29";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:16;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:17;s:4:"name";s:25:"referral_rules_visibility";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-02 17:35:50";}s:11:" * original";a:6:{s:2:"id";i:17;s:4:"name";s:25:"referral_rules_visibility";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-02 17:35:50";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:17;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:18;s:4:"name";s:13:"deposit_level";s:3:"val";s:1:"1";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:11:" * original";a:6:{s:2:"id";i:18;s:4:"name";s:13:"deposit_level";s:3:"val";s:1:"1";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:18;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:19;s:4:"name";s:17:"multiple_currency";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-04-17 11:24:33";}s:11:" * original";a:6:{s:2:"id";i:19;s:4:"name";s:17:"multiple_currency";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-04-17 11:24:33";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:19;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:20;s:4:"name";s:15:"transfer_status";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-25 09:24:02";}s:11:" * original";a:6:{s:2:"id";i:20;s:4:"name";s:15:"transfer_status";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-25 09:24:02";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:20;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:21;s:4:"name";s:18:"email_verification";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-02 17:21:20";}s:11:" * original";a:6:{s:2:"id";i:21;s:4:"name";s:18:"email_verification";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-02 17:21:20";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:21;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:22;s:4:"name";s:16:"kyc_verification";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-03 10:18:37";}s:11:" * original";a:6:{s:2:"id";i:22;s:4:"name";s:16:"kyc_verification";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-03 10:18:37";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:22;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:23;s:4:"name";s:15:"fa_verification";s:3:"val";s:1:"0";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-02 17:43:17";}s:11:" * original";a:6:{s:2:"id";i:23;s:4:"name";s:15:"fa_verification";s:3:"val";s:1:"0";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-02 17:43:17";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:23;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:24;s:4:"name";s:16:"otp_verification";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-25 09:24:02";}s:11:" * original";a:6:{s:2:"id";i:24;s:4:"name";s:16:"otp_verification";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-25 09:24:02";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:24;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:25;s:4:"name";s:16:"account_creation";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-02 17:35:50";}s:11:" * original";a:6:{s:2:"id";i:25;s:4:"name";s:16:"account_creation";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-02 17:35:50";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:25;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:26;s:4:"name";s:12:"user_deposit";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-02 17:35:50";}s:11:" * original";a:6:{s:2:"id";i:26;s:4:"name";s:12:"user_deposit";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-02 17:35:50";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:26;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:27;s:4:"name";s:14:"user_portfolio";s:3:"val";s:1:"1";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:11:" * original";a:6:{s:2:"id";i:27;s:4:"name";s:14:"user_portfolio";s:3:"val";s:1:"1";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:27;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:28;s:4:"name";s:13:"user_withdraw";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-02 17:35:50";}s:11:" * original";a:6:{s:2:"id";i:28;s:4:"name";s:13:"user_withdraw";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-02 17:35:50";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:28;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:29;s:4:"name";s:13:"user_pay_bill";s:3:"val";s:1:"1";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:11:" * original";a:6:{s:2:"id";i:29;s:4:"name";s:13:"user_pay_bill";s:3:"val";s:1:"1";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:29;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:30;s:4:"name";s:16:"sign_up_referral";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-02 17:35:50";}s:11:" * original";a:6:{s:2:"id";i:30;s:4:"name";s:16:"sign_up_referral";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-02 17:35:50";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:30;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:31;s:4:"name";s:21:"referral_signup_bonus";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-02 17:35:50";}s:11:" * original";a:6:{s:2:"id";i:31;s:4:"name";s:21:"referral_signup_bonus";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-02 17:35:50";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:31;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:32;s:4:"name";s:14:"site_animation";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-02 17:35:50";}s:11:" * original";a:6:{s:2:"id";i:32;s:4:"name";s:14:"site_animation";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-02 17:35:50";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:32;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:33;s:4:"name";s:11:"back_to_top";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-02 17:35:50";}s:11:" * original";a:6:{s:2:"id";i:33;s:4:"name";s:11:"back_to_top";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-02 17:35:50";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:33;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:34;s:4:"name";s:17:"language_switcher";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-02 17:35:50";}s:11:" * original";a:6:{s:2:"id";i:34;s:4:"name";s:17:"language_switcher";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-02 17:35:50";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:34;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:35;s:4:"name";s:12:"default_mode";s:3:"val";s:5:"light";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-14 11:24:24";}s:11:" * original";a:6:{s:2:"id";i:35;s:4:"name";s:12:"default_mode";s:3:"val";s:5:"light";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-14 11:24:24";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:35;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:36;s:4:"name";s:10:"debug_mode";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-03 14:53:31";}s:11:" * original";a:6:{s:2:"id";i:36;s:4:"name";s:10:"debug_mode";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-03 14:53:31";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:36;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:37;s:4:"name";s:16:"session_lifetime";s:3:"val";s:3:"120";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-04-24 11:02:00";}s:11:" * original";a:6:{s:2:"id";i:37;s:4:"name";s:16:"session_lifetime";s:3:"val";s:3:"120";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-04-24 11:02:00";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:37;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:38;s:4:"name";s:14:"referral_bonus";s:3:"val";s:2:"20";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 11:56:54";}s:11:" * original";a:6:{s:2:"id";i:38;s:4:"name";s:14:"referral_bonus";s:3:"val";s:2:"20";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 11:56:54";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:38;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:39;s:4:"name";s:12:"signup_bonus";s:3:"val";s:2:"20";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 11:56:54";}s:11:" * original";a:6:{s:2:"id";i:39;s:4:"name";s:12:"signup_bonus";s:3:"val";s:2:"20";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 11:56:54";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:39;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:40;s:4:"name";s:19:"transfer_min_amount";s:3:"val";s:2:"10";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 11:56:54";}s:11:" * original";a:6:{s:2:"id";i:40;s:4:"name";s:19:"transfer_min_amount";s:3:"val";s:2:"10";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 11:56:54";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:40;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:41;s:4:"name";s:19:"transfer_max_amount";s:3:"val";s:5:"20000";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 11:56:54";}s:11:" * original";a:6:{s:2:"id";i:41;s:4:"name";s:19:"transfer_max_amount";s:3:"val";s:5:"20000";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 11:56:54";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:41;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:42;s:4:"name";s:20:"fund_transfer_charge";s:3:"val";s:1:"4";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 11:56:54";}s:11:" * original";a:6:{s:2:"id";i:42;s:4:"name";s:20:"fund_transfer_charge";s:3:"val";s:1:"4";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 11:56:54";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:42;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:43;s:4:"name";s:25:"fund_transfer_charge_type";s:3:"val";s:10:"percentage";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 11:56:54";}s:11:" * original";a:6:{s:2:"id";i:43;s:4:"name";s:25:"fund_transfer_charge_type";s:3:"val";s:10:"percentage";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 11:56:54";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:43;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:44;s:4:"name";s:18:"withdraw_day_limit";s:3:"val";s:2:"20";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 11:56:54";}s:11:" * original";a:6:{s:2:"id";i:44;s:4:"name";s:18:"withdraw_day_limit";s:3:"val";s:2:"20";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 11:56:54";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:44;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:45;s:4:"name";s:11:"kyc_deposit";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-25 09:17:07";}s:11:" * original";a:6:{s:2:"id";i:45;s:4:"name";s:11:"kyc_deposit";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-25 09:17:07";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:45;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:46;s:4:"name";s:17:"kyc_fund_transfer";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-25 09:21:54";}s:11:" * original";a:6:{s:2:"id";i:46;s:4:"name";s:17:"kyc_fund_transfer";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-25 09:21:54";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:46;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:47;s:4:"name";s:7:"kyc_dps";s:3:"val";s:1:"0";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-25 09:17:07";}s:11:" * original";a:6:{s:2:"id";i:47;s:4:"name";s:7:"kyc_dps";s:3:"val";s:1:"0";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-25 09:17:07";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:47;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:48;s:4:"name";s:7:"kyc_fdr";s:3:"val";s:1:"0";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-25 09:17:07";}s:11:" * original";a:6:{s:2:"id";i:48;s:4:"name";s:7:"kyc_fdr";s:3:"val";s:1:"0";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-25 09:17:07";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:48;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:49;s:4:"name";s:8:"kyc_loan";s:3:"val";s:1:"0";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-25 09:17:07";}s:11:" * original";a:6:{s:2:"id";i:49;s:4:"name";s:8:"kyc_loan";s:3:"val";s:1:"0";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-25 09:17:07";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:49;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:50;s:4:"name";s:12:"kyc_pay_bill";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-12-01 08:45:56";}s:11:" * original";a:6:{s:2:"id";i:50;s:4:"name";s:12:"kyc_pay_bill";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-12-01 08:45:56";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:50;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:51;s:4:"name";s:12:"kyc_withdraw";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-04-24 11:02:32";}s:11:" * original";a:6:{s:2:"id";i:51;s:4:"name";s:12:"kyc_withdraw";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-04-24 11:02:32";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:51;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:52;s:4:"name";s:23:"deposit_passcode_status";s:3:"val";s:1:"0";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:11:" * original";a:6:{s:2:"id";i:52;s:4:"name";s:23:"deposit_passcode_status";s:3:"val";s:1:"0";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:52;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:53;s:4:"name";s:29:"fund_transfer_passcode_status";s:3:"val";s:1:"0";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:11:" * original";a:6:{s:2:"id";i:53;s:4:"name";s:29:"fund_transfer_passcode_status";s:3:"val";s:1:"0";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:53;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:54;s:4:"name";s:19:"dps_passcode_status";s:3:"val";s:1:"0";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:11:" * original";a:6:{s:2:"id";i:54;s:4:"name";s:19:"dps_passcode_status";s:3:"val";s:1:"0";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:54;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:55;s:4:"name";s:19:"fdr_passcode_status";s:3:"val";s:1:"0";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:11:" * original";a:6:{s:2:"id";i:55;s:4:"name";s:19:"fdr_passcode_status";s:3:"val";s:1:"0";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:55;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:56;s:4:"name";s:20:"loan_passcode_status";s:3:"val";s:1:"0";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:11:" * original";a:6:{s:2:"id";i:56;s:4:"name";s:20:"loan_passcode_status";s:3:"val";s:1:"0";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:56;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:57;s:4:"name";s:24:"pay_bill_passcode_status";s:3:"val";s:1:"0";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:11:" * original";a:6:{s:2:"id";i:57;s:4:"name";s:24:"pay_bill_passcode_status";s:3:"val";s:1:"0";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:57;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:58;s:4:"name";s:24:"withdraw_passcode_status";s:3:"val";s:1:"0";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:11:" * original";a:6:{s:2:"id";i:58;s:4:"name";s:24:"withdraw_passcode_status";s:3:"val";s:1:"0";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:58;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:59;s:4:"name";s:25:"inactive_account_disabled";s:3:"val";s:1:"0";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-04-24 11:02:08";}s:11:" * original";a:6:{s:2:"id";i:59;s:4:"name";s:25:"inactive_account_disabled";s:3:"val";s:1:"0";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-04-24 11:02:08";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:59;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:60;s:4:"name";s:13:"inactive_days";s:3:"val";s:2:"30";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-04-24 11:01:16";}s:11:" * original";a:6:{s:2:"id";i:60;s:4:"name";s:13:"inactive_days";s:3:"val";s:2:"30";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-04-24 11:01:16";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:60;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:61;s:4:"name";s:21:"inactive_account_fees";s:3:"val";s:1:"1";s:4:"type";s:6:"switch";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:11:" * original";a:6:{s:2:"id";i:61;s:4:"name";s:21:"inactive_account_fees";s:3:"val";s:1:"1";s:4:"type";s:6:"switch";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:61;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:62;s:4:"name";s:10:"fee_amount";s:3:"val";s:1:"5";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:11:" * original";a:6:{s:2:"id";i:62;s:4:"name";s:10:"fee_amount";s:3:"val";s:1:"5";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:62;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:63;s:4:"name";s:15:"email_from_name";s:3:"val";s:5:"Tdevs";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-03-04 11:54:45";}s:11:" * original";a:6:{s:2:"id";i:63;s:4:"name";s:15:"email_from_name";s:3:"val";s:5:"Tdevs";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-03-04 11:54:45";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:63;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:64;s:4:"name";s:18:"email_from_address";s:3:"val";s:19:"<EMAIL>";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-03-04 11:55:48";}s:11:" * original";a:6:{s:2:"id";i:64;s:4:"name";s:18:"email_from_address";s:3:"val";s:19:"<EMAIL>";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-03-04 11:55:48";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:64;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:65;s:4:"name";s:14:"mailing_driver";s:3:"val";s:4:"smtp";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-03-04 11:54:45";}s:11:" * original";a:6:{s:2:"id";i:65;s:4:"name";s:14:"mailing_driver";s:3:"val";s:4:"smtp";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-03-04 11:54:45";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:65;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:66;s:4:"name";s:13:"mail_username";s:3:"val";s:14:"bb9ee8c66e312a";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-03-04 11:54:45";}s:11:" * original";a:6:{s:2:"id";i:66;s:4:"name";s:13:"mail_username";s:3:"val";s:14:"bb9ee8c66e312a";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-03-04 11:54:45";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:66;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:67;s:4:"name";s:13:"mail_password";s:3:"val";s:14:"a03043c580754b";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-03-04 11:54:45";}s:11:" * original";a:6:{s:2:"id";i:67;s:4:"name";s:13:"mail_password";s:3:"val";s:14:"a03043c580754b";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-03-04 11:54:45";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:67;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:68;s:4:"name";s:9:"mail_host";s:3:"val";s:9:"localhost";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-05-21 11:26:46";}s:11:" * original";a:6:{s:2:"id";i:68;s:4:"name";s:9:"mail_host";s:3:"val";s:9:"localhost";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-05-21 11:26:46";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:68;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:69;s:4:"name";s:9:"mail_port";s:3:"val";s:4:"1025";s:4:"type";s:7:"integer";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-05-21 11:26:46";}s:11:" * original";a:6:{s:2:"id";i:69;s:4:"name";s:9:"mail_port";s:3:"val";s:4:"1025";s:4:"type";s:7:"integer";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-05-21 11:26:46";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:69;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:70;s:4:"name";s:11:"mail_secure";s:3:"val";s:3:"tls";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-03-04 11:54:45";}s:11:" * original";a:6:{s:2:"id";i:70;s:4:"name";s:11:"mail_secure";s:3:"val";s:3:"tls";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-03-04 11:54:45";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:70;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:71;s:4:"name";s:36:"suggested_regular_license_price_from";s:3:"val";s:2:"10";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:11:" * original";a:6:{s:2:"id";i:71;s:4:"name";s:36:"suggested_regular_license_price_from";s:3:"val";s:2:"10";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:71;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:72;s:4:"name";s:34:"suggested_regular_license_price_to";s:3:"val";s:2:"15";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:11:" * original";a:6:{s:2:"id";i:72;s:4:"name";s:34:"suggested_regular_license_price_to";s:3:"val";s:2:"15";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:72;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:73;s:4:"name";s:37:"suggested_extended_license_price_from";s:3:"val";s:2:"20";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:11:" * original";a:6:{s:2:"id";i:73;s:4:"name";s:37:"suggested_extended_license_price_from";s:3:"val";s:2:"20";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:73;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:74;s:4:"name";s:35:"suggested_extended_license_price_to";s:3:"val";s:2:"50";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:11:" * original";a:6:{s:2:"id";i:74;s:4:"name";s:35:"suggested_extended_license_price_to";s:3:"val";s:2:"50";s:4:"type";s:8:"checkbox";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:74;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:75;s:4:"name";s:16:"maintenance_mode";s:3:"val";s:1:"0";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-05-17 15:58:07";}s:11:" * original";a:6:{s:2:"id";i:75;s:4:"name";s:16:"maintenance_mode";s:3:"val";s:1:"0";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-05-17 15:58:07";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:75;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:76;s:4:"name";s:10:"secret_key";s:3:"val";s:6:"secret";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-04-15 17:20:34";}s:11:" * original";a:6:{s:2:"id";i:76;s:4:"name";s:10:"secret_key";s:3:"val";s:6:"secret";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-04-15 17:20:34";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:76;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:77;s:4:"name";s:17:"maintenance_title";s:3:"val";s:25:"Site is under maintenance";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-04 10:58:24";}s:11:" * original";a:6:{s:2:"id";i:77;s:4:"name";s:17:"maintenance_title";s:3:"val";s:25:"Site is under maintenance";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-06-04 10:58:24";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:77;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:78;s:4:"name";s:16:"maintenance_text";s:3:"val";s:41:"Sorry for interrupt! Site will live soon.";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-04-15 17:20:34";}s:11:" * original";a:6:{s:2:"id";i:78;s:4:"name";s:16:"maintenance_text";s:3:"val";s:41:"Sorry for interrupt! Site will live soon.";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-04-15 17:20:34";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:78;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:79;s:4:"name";s:11:"gdpr_status";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 08:33:49";}s:11:" * original";a:6:{s:2:"id";i:79;s:4:"name";s:11:"gdpr_status";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 08:33:49";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:79;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:80;s:4:"name";s:9:"gdpr_text";s:3:"val";s:166:"Please allow us to collect data about how you use our website. We will use it to improve our website, make your browsing experience and our business decisions better.";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-03-27 09:50:12";}s:11:" * original";a:6:{s:2:"id";i:80;s:4:"name";s:9:"gdpr_text";s:3:"val";s:166:"Please allow us to collect data about how you use our website. We will use it to improve our website, make your browsing experience and our business decisions better.";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-03-27 09:50:12";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:80;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:81;s:4:"name";s:17:"gdpr_button_label";s:3:"val";s:10:"Learn More";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 08:33:49";}s:11:" * original";a:6:{s:2:"id";i:81;s:4:"name";s:17:"gdpr_button_label";s:3:"val";s:10:"Learn More";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-21 08:33:49";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:81;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:82;s:4:"name";s:15:"gdpr_button_url";s:3:"val";s:20:"/page/privacy-policy";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-04-24 11:16:21";}s:11:" * original";a:6:{s:2:"id";i:82;s:4:"name";s:15:"gdpr_button_url";s:3:"val";s:20:"/page/privacy-policy";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-04-24 11:16:21";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:82;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:83;s:4:"name";s:16:"meta_description";s:3:"val";s:255:"OrexCoin is a modern cryptocurrency mining and earnings system designed to help users generate passive income through digital assets. Our platform offers secure mining solutions, investment opportunities, and comprehensive cryptocurrency management tools.";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-05-30 10:48:24";}s:11:" * original";a:6:{s:2:"id";i:83;s:4:"name";s:16:"meta_description";s:3:"val";s:255:"OrexCoin is a modern cryptocurrency mining and earnings system designed to help users generate passive income through digital assets. Our platform offers secure mining solutions, investment opportunities, and comprehensive cryptocurrency management tools.";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-05-30 10:48:24";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:83;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:84;s:4:"name";s:13:"meta_keywords";s:3:"val";s:187:"orexcoin, cryptocurrency mining, crypto earnings, bitcoin mining, ethereum mining, passive income, digital assets, blockchain technology, crypto investment, mining platform, crypto wallet";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-05-30 10:48:24";}s:11:" * original";a:6:{s:2:"id";i:84;s:4:"name";s:13:"meta_keywords";s:3:"val";s:187:"orexcoin, cryptocurrency mining, crypto earnings, bitcoin mining, ethereum mining, passive income, digital assets, blockchain technology, crypto investment, mining platform, crypto wallet";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2025-05-30 10:48:24";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:84;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:85;s:4:"name";s:27:"affiliate_commission_charge";s:3:"val";s:1:"4";s:4:"type";s:4:"text";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:11:" * original";a:6:{s:2:"id";i:85;s:4:"name";s:27:"affiliate_commission_charge";s:3:"val";s:1:"4";s:4:"type";s:4:"text";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:85;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:86;s:4:"name";s:32:"affiliate_commission_charge_type";s:3:"val";s:10:"percentage";s:4:"type";s:4:"text";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:11:" * original";a:6:{s:2:"id";i:86;s:4:"name";s:32:"affiliate_commission_charge_type";s:3:"val";s:10:"percentage";s:4:"type";s:4:"text";s:10:"created_at";s:19:"2024-11-20 04:45:36";s:10:"updated_at";s:19:"2024-11-20 04:45:36";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:86;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:87;s:4:"name";s:15:"exchange_charge";s:3:"val";s:1:"2";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2024-11-27 08:49:27";s:10:"updated_at";s:19:"2025-02-27 18:03:06";}s:11:" * original";a:6:{s:2:"id";i:87;s:4:"name";s:15:"exchange_charge";s:3:"val";s:1:"2";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2024-11-27 08:49:27";s:10:"updated_at";s:19:"2025-02-27 18:03:06";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:87;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:88;s:4:"name";s:20:"exchange_charge_type";s:3:"val";s:10:"percentage";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2024-11-27 08:49:27";s:10:"updated_at";s:19:"2025-02-27 18:03:06";}s:11:" * original";a:6:{s:2:"id";i:88;s:4:"name";s:20:"exchange_charge_type";s:3:"val";s:10:"percentage";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2024-11-27 08:49:27";s:10:"updated_at";s:19:"2025-02-27 18:03:06";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:88;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:89;s:4:"name";s:14:"cashout_charge";s:3:"val";s:1:"5";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-20 09:37:57";s:10:"updated_at";s:19:"2025-02-27 15:14:04";}s:11:" * original";a:6:{s:2:"id";i:89;s:4:"name";s:14:"cashout_charge";s:3:"val";s:1:"5";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-20 09:37:57";s:10:"updated_at";s:19:"2025-02-27 15:14:04";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:89;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:90;s:4:"name";s:15:"cashout_minimum";s:3:"val";s:3:"100";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-20 09:37:58";s:10:"updated_at";s:19:"2025-02-27 15:12:02";}s:11:" * original";a:6:{s:2:"id";i:90;s:4:"name";s:15:"cashout_minimum";s:3:"val";s:3:"100";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-20 09:37:58";s:10:"updated_at";s:19:"2025-02-27 15:12:02";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:90;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:91;s:4:"name";s:15:"cashout_maximum";s:3:"val";s:5:"10000";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-20 09:37:58";s:10:"updated_at";s:19:"2025-02-20 09:37:58";}s:11:" * original";a:6:{s:2:"id";i:91;s:4:"name";s:15:"cashout_maximum";s:3:"val";s:5:"10000";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-20 09:37:58";s:10:"updated_at";s:19:"2025-02-20 09:37:58";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:91;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:92;s:4:"name";s:19:"cashout_daily_limit";s:3:"val";s:4:"1000";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-20 09:37:58";s:10:"updated_at";s:19:"2025-02-27 16:02:29";}s:11:" * original";a:6:{s:2:"id";i:92;s:4:"name";s:19:"cashout_daily_limit";s:3:"val";s:4:"1000";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-20 09:37:58";s:10:"updated_at";s:19:"2025-02-27 16:02:29";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:92;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:93;s:4:"name";s:21:"cashout_monthly_limit";s:3:"val";s:5:"10000";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-20 09:37:58";s:10:"updated_at";s:19:"2025-02-27 15:44:40";}s:11:" * original";a:6:{s:2:"id";i:93;s:4:"name";s:21:"cashout_monthly_limit";s:3:"val";s:5:"10000";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-20 09:37:58";s:10:"updated_at";s:19:"2025-02-27 15:44:40";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:93;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:94;s:4:"name";s:19:"cashout_charge_type";s:3:"val";s:10:"percentage";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-20 09:40:15";s:10:"updated_at";s:19:"2025-02-27 15:14:54";}s:11:" * original";a:6:{s:2:"id";i:94;s:4:"name";s:19:"cashout_charge_type";s:3:"val";s:10:"percentage";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-20 09:40:15";s:10:"updated_at";s:19:"2025-02-27 15:14:54";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:94;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:95;s:4:"name";s:11:"gift_charge";s:3:"val";s:2:"10";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-22 10:20:22";s:10:"updated_at";s:19:"2025-02-22 10:20:51";}s:11:" * original";a:6:{s:2:"id";i:95;s:4:"name";s:11:"gift_charge";s:3:"val";s:2:"10";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-22 10:20:22";s:10:"updated_at";s:19:"2025-02-22 10:20:51";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:95;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:96;s:4:"name";s:16:"gift_charge_type";s:3:"val";s:5:"fixed";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-22 10:20:22";s:10:"updated_at";s:19:"2025-02-26 14:41:30";}s:11:" * original";a:6:{s:2:"id";i:96;s:4:"name";s:16:"gift_charge_type";s:3:"val";s:5:"fixed";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-22 10:20:22";s:10:"updated_at";s:19:"2025-02-26 14:41:30";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:96;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:97;s:4:"name";s:12:"gift_minimum";s:3:"val";s:3:"100";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-22 10:20:22";s:10:"updated_at";s:19:"2025-02-22 10:20:22";}s:11:" * original";a:6:{s:2:"id";i:97;s:4:"name";s:12:"gift_minimum";s:3:"val";s:3:"100";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-22 10:20:22";s:10:"updated_at";s:19:"2025-02-22 10:20:22";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:97;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:98;s:4:"name";s:12:"gift_maximum";s:3:"val";s:4:"1000";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-22 10:20:22";s:10:"updated_at";s:19:"2025-02-22 10:20:39";}s:11:" * original";a:6:{s:2:"id";i:98;s:4:"name";s:12:"gift_maximum";s:3:"val";s:4:"1000";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-22 10:20:22";s:10:"updated_at";s:19:"2025-02-22 10:20:39";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:98;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:99;s:4:"name";s:16:"gift_daily_limit";s:3:"val";s:2:"10";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-22 10:20:22";s:10:"updated_at";s:19:"2025-02-22 14:56:39";}s:11:" * original";a:6:{s:2:"id";i:99;s:4:"name";s:16:"gift_daily_limit";s:3:"val";s:2:"10";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-22 10:20:22";s:10:"updated_at";s:19:"2025-02-22 14:56:39";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:99;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:100;s:4:"name";s:14:"invoice_charge";s:3:"val";s:1:"2";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-22 14:29:54";s:10:"updated_at";s:19:"2025-02-22 14:45:24";}s:11:" * original";a:6:{s:2:"id";i:100;s:4:"name";s:14:"invoice_charge";s:3:"val";s:1:"2";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-22 14:29:54";s:10:"updated_at";s:19:"2025-02-22 14:45:24";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:100;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:101;s:4:"name";s:19:"invoice_charge_type";s:3:"val";s:5:"fixed";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-22 14:29:54";s:10:"updated_at";s:19:"2025-02-22 14:45:19";}s:11:" * original";a:6:{s:2:"id";i:101;s:4:"name";s:19:"invoice_charge_type";s:3:"val";s:5:"fixed";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-22 14:29:54";s:10:"updated_at";s:19:"2025-02-22 14:45:19";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:101;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:102;s:4:"name";s:19:"invoice_daily_limit";s:3:"val";s:2:"10";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-22 14:29:54";s:10:"updated_at";s:19:"2025-02-22 14:30:13";}s:11:" * original";a:6:{s:2:"id";i:102;s:4:"name";s:19:"invoice_daily_limit";s:3:"val";s:2:"10";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-22 14:29:54";s:10:"updated_at";s:19:"2025-02-22 14:30:13";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:102;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:103;s:4:"name";s:20:"exchange_daily_limit";s:3:"val";s:2:"20";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-22 14:45:12";s:10:"updated_at";s:19:"2025-02-22 14:45:12";}s:11:" * original";a:6:{s:2:"id";i:103;s:4:"name";s:20:"exchange_daily_limit";s:3:"val";s:2:"20";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-22 14:45:12";s:10:"updated_at";s:19:"2025-02-22 14:45:12";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:103;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:104;s:4:"name";s:24:"cashout_agent_commission";s:3:"val";s:1:"2";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-22 15:16:37";s:10:"updated_at";s:19:"2025-02-22 15:16:46";}s:11:" * original";a:6:{s:2:"id";i:104;s:4:"name";s:24:"cashout_agent_commission";s:3:"val";s:1:"2";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-22 15:16:37";s:10:"updated_at";s:19:"2025-02-22 15:16:46";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:104;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:105;s:4:"name";s:29:"cashout_agent_commission_type";s:3:"val";s:10:"percentage";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-22 15:16:37";s:10:"updated_at";s:19:"2025-02-22 15:16:40";}s:11:" * original";a:6:{s:2:"id";i:105;s:4:"name";s:29:"cashout_agent_commission_type";s:3:"val";s:10:"percentage";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-22 15:16:37";s:10:"updated_at";s:19:"2025-02-22 15:16:40";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:105;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:106;s:4:"name";s:18:"api_payment_charge";s:3:"val";s:1:"5";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-23 10:37:37";s:10:"updated_at";s:19:"2025-02-23 10:37:37";}s:11:" * original";a:6:{s:2:"id";i:106;s:4:"name";s:18:"api_payment_charge";s:3:"val";s:1:"5";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-23 10:37:37";s:10:"updated_at";s:19:"2025-02-23 10:37:37";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:106;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:107;s:4:"name";s:23:"api_payment_charge_type";s:3:"val";s:5:"fixed";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-23 10:37:37";s:10:"updated_at";s:19:"2025-02-23 10:37:37";}s:11:" * original";a:6:{s:2:"id";i:107;s:4:"name";s:23:"api_payment_charge_type";s:3:"val";s:5:"fixed";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-23 10:37:37";s:10:"updated_at";s:19:"2025-02-23 10:37:37";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:107;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:108;s:4:"name";s:24:"user_make_payment_charge";s:3:"val";s:1:"5";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-24 15:11:25";s:10:"updated_at";s:19:"2025-02-24 15:11:25";}s:11:" * original";a:6:{s:2:"id";i:108;s:4:"name";s:24:"user_make_payment_charge";s:3:"val";s:1:"5";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-24 15:11:25";s:10:"updated_at";s:19:"2025-02-24 15:11:25";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:108;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:109;s:4:"name";s:29:"user_make_payment_charge_type";s:3:"val";s:10:"percentage";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-24 15:11:25";s:10:"updated_at";s:19:"2025-02-24 15:37:26";}s:11:" * original";a:6:{s:2:"id";i:109;s:4:"name";s:29:"user_make_payment_charge_type";s:3:"val";s:10:"percentage";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-24 15:11:25";s:10:"updated_at";s:19:"2025-02-24 15:37:26";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:109;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:110;s:4:"name";s:28:"merchant_make_payment_charge";s:3:"val";s:1:"2";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-24 15:11:25";s:10:"updated_at";s:19:"2025-02-24 16:05:50";}s:11:" * original";a:6:{s:2:"id";i:110;s:4:"name";s:28:"merchant_make_payment_charge";s:3:"val";s:1:"2";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-24 15:11:25";s:10:"updated_at";s:19:"2025-02-24 16:05:50";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:110;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:111;s:4:"name";s:33:"merchant_make_payment_charge_type";s:3:"val";s:5:"fixed";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-24 15:11:25";s:10:"updated_at";s:19:"2025-02-24 16:05:50";}s:11:" * original";a:6:{s:2:"id";i:111;s:4:"name";s:33:"merchant_make_payment_charge_type";s:3:"val";s:5:"fixed";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-24 15:11:25";s:10:"updated_at";s:19:"2025-02-24 16:05:50";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:111;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:112;s:4:"name";s:20:"request_money_charge";s:3:"val";s:1:"5";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-26 10:53:12";s:10:"updated_at";s:19:"2025-02-26 10:53:12";}s:11:" * original";a:6:{s:2:"id";i:112;s:4:"name";s:20:"request_money_charge";s:3:"val";s:1:"5";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-26 10:53:12";s:10:"updated_at";s:19:"2025-02-26 10:53:12";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:112;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:113;s:4:"name";s:25:"request_money_charge_type";s:3:"val";s:10:"percentage";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-26 10:53:12";s:10:"updated_at";s:19:"2025-02-26 10:53:12";}s:11:" * original";a:6:{s:2:"id";i:113;s:4:"name";s:25:"request_money_charge_type";s:3:"val";s:10:"percentage";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-26 10:53:12";s:10:"updated_at";s:19:"2025-02-26 10:53:12";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:113;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:114;s:4:"name";s:25:"request_money_daily_limit";s:3:"val";s:2:"10";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-26 10:53:12";s:10:"updated_at";s:19:"2025-02-26 10:53:12";}s:11:" * original";a:6:{s:2:"id";i:114;s:4:"name";s:25:"request_money_daily_limit";s:3:"val";s:2:"10";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-26 10:53:12";s:10:"updated_at";s:19:"2025-02-26 10:53:12";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:114;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:115;s:4:"name";s:15:"transfer_charge";s:3:"val";s:1:"5";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-27 10:49:05";s:10:"updated_at";s:19:"2025-02-27 10:49:05";}s:11:" * original";a:6:{s:2:"id";i:115;s:4:"name";s:15:"transfer_charge";s:3:"val";s:1:"5";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-27 10:49:05";s:10:"updated_at";s:19:"2025-02-27 10:49:05";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:115;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:116;s:4:"name";s:20:"transfer_charge_type";s:3:"val";s:10:"percentage";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-27 10:49:06";s:10:"updated_at";s:19:"2025-02-27 10:49:06";}s:11:" * original";a:6:{s:2:"id";i:116;s:4:"name";s:20:"transfer_charge_type";s:3:"val";s:10:"percentage";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-27 10:49:06";s:10:"updated_at";s:19:"2025-02-27 10:49:06";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:116;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:117;s:4:"name";s:16:"transfer_minimum";s:3:"val";s:2:"10";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-27 10:49:06";s:10:"updated_at";s:19:"2025-02-27 10:49:06";}s:11:" * original";a:6:{s:2:"id";i:117;s:4:"name";s:16:"transfer_minimum";s:3:"val";s:2:"10";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-27 10:49:06";s:10:"updated_at";s:19:"2025-02-27 10:49:06";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:117;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:118;s:4:"name";s:16:"transfer_maximum";s:3:"val";s:4:"1000";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-27 10:49:06";s:10:"updated_at";s:19:"2025-02-27 10:49:06";}s:11:" * original";a:6:{s:2:"id";i:118;s:4:"name";s:16:"transfer_maximum";s:3:"val";s:4:"1000";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-27 10:49:06";s:10:"updated_at";s:19:"2025-02-27 10:49:06";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:118;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:119;s:4:"name";s:20:"transfer_daily_limit";s:3:"val";s:5:"10000";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-27 10:49:06";s:10:"updated_at";s:19:"2025-02-27 10:49:09";}s:11:" * original";a:6:{s:2:"id";i:119;s:4:"name";s:20:"transfer_daily_limit";s:3:"val";s:5:"10000";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-02-27 10:49:06";s:10:"updated_at";s:19:"2025-02-27 10:49:09";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:119;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:120;s:4:"name";s:22:"site_currency_decimals";s:3:"val";s:1:"4";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2025-03-06 09:06:55";s:10:"updated_at";s:19:"2025-05-31 11:25:44";}s:11:" * original";a:6:{s:2:"id";i:120;s:4:"name";s:22:"site_currency_decimals";s:3:"val";s:1:"4";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2025-03-06 09:06:55";s:10:"updated_at";s:19:"2025-05-31 11:25:44";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:120;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:121;s:4:"name";s:7:"deposit";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-03-06 12:09:34";s:10:"updated_at";s:19:"2025-03-06 12:59:30";}s:11:" * original";a:6:{s:2:"id";i:121;s:4:"name";s:7:"deposit";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-03-06 12:09:34";s:10:"updated_at";s:19:"2025-03-06 12:59:30";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:121;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:122;s:4:"name";s:8:"transfer";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-03-06 12:11:00";s:10:"updated_at";s:19:"2025-03-06 12:41:06";}s:11:" * original";a:6:{s:2:"id";i:122;s:4:"name";s:8:"transfer";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-03-06 12:11:00";s:10:"updated_at";s:19:"2025-03-06 12:41:06";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:122;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:123;s:4:"name";s:8:"cash_out";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-03-06 12:11:02";s:10:"updated_at";s:19:"2025-03-06 12:11:02";}s:11:" * original";a:6:{s:2:"id";i:123;s:4:"name";s:8:"cash_out";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-03-06 12:11:02";s:10:"updated_at";s:19:"2025-03-06 12:11:02";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:123;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:124;s:4:"name";s:11:"invoice_pay";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-03-06 12:11:04";s:10:"updated_at";s:19:"2025-03-06 12:58:52";}s:11:" * original";a:6:{s:2:"id";i:124;s:4:"name";s:11:"invoice_pay";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-03-06 12:11:04";s:10:"updated_at";s:19:"2025-03-06 12:58:52";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:124;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:125;s:4:"name";s:8:"exchange";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-03-06 12:11:05";s:10:"updated_at";s:19:"2025-03-06 12:58:54";}s:11:" * original";a:6:{s:2:"id";i:125;s:4:"name";s:8:"exchange";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-03-06 12:11:05";s:10:"updated_at";s:19:"2025-03-06 12:58:54";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:125;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:126;s:4:"name";s:11:"create_gift";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-03-06 12:11:06";s:10:"updated_at";s:19:"2025-03-06 12:58:56";}s:11:" * original";a:6:{s:2:"id";i:126;s:4:"name";s:11:"create_gift";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-03-06 12:11:06";s:10:"updated_at";s:19:"2025-03-06 12:58:56";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:126;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:127;s:4:"name";s:20:"request_money_accept";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-03-06 12:11:08";s:10:"updated_at";s:19:"2025-03-06 12:58:51";}s:11:" * original";a:6:{s:2:"id";i:127;s:4:"name";s:20:"request_money_accept";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-03-06 12:11:08";s:10:"updated_at";s:19:"2025-03-06 12:58:51";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:127;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:128;s:4:"name";s:7:"payment";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-03-06 12:11:11";s:10:"updated_at";s:19:"2025-03-06 12:58:50";}s:11:" * original";a:6:{s:2:"id";i:128;s:4:"name";s:7:"payment";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-03-06 12:11:11";s:10:"updated_at";s:19:"2025-03-06 12:58:50";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:128;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:129;s:4:"name";s:14:"referral_rules";s:3:"val";s:197:"[{"icon":"tick","rule":"Referrer gets a reward when the referred user makes a successful first deposit."},{"icon":"cross","rule":"No reward if the referred user fails KYC or cancels the deposit."}]";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2025-03-06 12:44:52";s:10:"updated_at";s:19:"2025-04-15 17:45:50";}s:11:" * original";a:6:{s:2:"id";i:129;s:4:"name";s:14:"referral_rules";s:3:"val";s:197:"[{"icon":"tick","rule":"Referrer gets a reward when the referred user makes a successful first deposit."},{"icon":"cross","rule":"No reward if the referred user fails KYC or cancels the deposit."}]";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2025-03-06 12:44:52";s:10:"updated_at";s:19:"2025-04-15 17:45:50";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:129;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:130;s:4:"name";s:9:"preloader";s:3:"val";s:1:"0";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-06 11:33:36";s:10:"updated_at";s:19:"2025-04-28 12:51:20";}s:11:" * original";a:6:{s:2:"id";i:130;s:4:"name";s:9:"preloader";s:3:"val";s:1:"0";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-06 11:33:36";s:10:"updated_at";s:19:"2025-04-28 12:51:20";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:130;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:131;s:4:"name";s:11:"kyc_cashout";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:08:59";s:10:"updated_at";s:19:"2025-04-10 12:08:59";}s:11:" * original";a:6:{s:2:"id";i:131;s:4:"name";s:11:"kyc_cashout";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:08:59";s:10:"updated_at";s:19:"2025-04-10 12:08:59";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:131;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:132;s:4:"name";s:12:"kyc_exchange";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:08:59";s:10:"updated_at";s:19:"2025-04-10 12:08:59";}s:11:" * original";a:6:{s:2:"id";i:132;s:4:"name";s:12:"kyc_exchange";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:08:59";s:10:"updated_at";s:19:"2025-04-10 12:08:59";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:132;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:133;s:4:"name";s:11:"kyc_payment";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:08:59";s:10:"updated_at";s:19:"2025-04-10 12:08:59";}s:11:" * original";a:6:{s:2:"id";i:133;s:4:"name";s:11:"kyc_payment";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:08:59";s:10:"updated_at";s:19:"2025-04-10 12:08:59";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:133;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:134;s:4:"name";s:17:"kyc_request_money";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:08:59";s:10:"updated_at";s:19:"2025-04-10 12:08:59";}s:11:" * original";a:6:{s:2:"id";i:134;s:4:"name";s:17:"kyc_request_money";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:08:59";s:10:"updated_at";s:19:"2025-04-10 12:08:59";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:134;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:135;s:4:"name";s:15:"kyc_create_gift";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:08:59";s:10:"updated_at";s:19:"2025-04-10 12:08:59";}s:11:" * original";a:6:{s:2:"id";i:135;s:4:"name";s:15:"kyc_create_gift";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:08:59";s:10:"updated_at";s:19:"2025-04-10 12:08:59";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:135;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:136;s:4:"name";s:11:"kyc_invoice";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:08:59";s:10:"updated_at";s:19:"2025-04-10 12:08:59";}s:11:" * original";a:6:{s:2:"id";i:136;s:4:"name";s:11:"kyc_invoice";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:08:59";s:10:"updated_at";s:19:"2025-04-10 12:08:59";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:136;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:137;s:4:"name";s:8:"kyc_gift";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:09:33";s:10:"updated_at";s:19:"2025-04-10 12:09:33";}s:11:" * original";a:6:{s:2:"id";i:137;s:4:"name";s:8:"kyc_gift";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:09:33";s:10:"updated_at";s:19:"2025-04-10 12:09:33";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:137;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:138;s:4:"name";s:13:"user_transfer";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:14:17";s:10:"updated_at";s:19:"2025-04-10 12:14:17";}s:11:" * original";a:6:{s:2:"id";i:138;s:4:"name";s:13:"user_transfer";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:14:17";s:10:"updated_at";s:19:"2025-04-10 12:14:17";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:138;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:139;s:4:"name";s:12:"user_cashout";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:14:17";s:10:"updated_at";s:19:"2025-04-10 12:14:17";}s:11:" * original";a:6:{s:2:"id";i:139;s:4:"name";s:12:"user_cashout";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:14:17";s:10:"updated_at";s:19:"2025-04-10 12:14:17";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:139;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:140;s:4:"name";s:9:"user_gift";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:14:17";s:10:"updated_at";s:19:"2025-04-10 12:14:17";}s:11:" * original";a:6:{s:2:"id";i:140;s:4:"name";s:9:"user_gift";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:14:17";s:10:"updated_at";s:19:"2025-04-10 12:14:17";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:140;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:141;s:4:"name";s:12:"user_payment";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:14:17";s:10:"updated_at";s:19:"2025-04-10 12:14:17";}s:11:" * original";a:6:{s:2:"id";i:141;s:4:"name";s:12:"user_payment";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:14:17";s:10:"updated_at";s:19:"2025-04-10 12:14:17";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:141;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:142;s:4:"name";s:12:"user_invoice";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:14:17";s:10:"updated_at";s:19:"2025-04-17 11:06:15";}s:11:" * original";a:6:{s:2:"id";i:142;s:4:"name";s:12:"user_invoice";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:14:17";s:10:"updated_at";s:19:"2025-04-17 11:06:15";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:142;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:143;s:4:"name";s:18:"user_request_money";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:14:17";s:10:"updated_at";s:19:"2025-04-10 12:16:42";}s:11:" * original";a:6:{s:2:"id";i:143;s:4:"name";s:18:"user_request_money";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:14:17";s:10:"updated_at";s:19:"2025-04-10 12:16:42";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:143;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:144;s:4:"name";s:13:"user_exchange";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:14:17";s:10:"updated_at";s:19:"2025-04-10 12:19:18";}s:11:" * original";a:6:{s:2:"id";i:144;s:4:"name";s:13:"user_exchange";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:14:17";s:10:"updated_at";s:19:"2025-04-10 12:19:18";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:144;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:145;s:4:"name";s:11:"user_ticket";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:14:17";s:10:"updated_at";s:19:"2025-06-18 17:53:56";}s:11:" * original";a:6:{s:2:"id";i:145;s:4:"name";s:11:"user_ticket";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:14:17";s:10:"updated_at";s:19:"2025-06-18 17:53:56";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:145;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:146;s:4:"name";s:10:"kyc_wallet";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:30:30";s:10:"updated_at";s:19:"2025-04-10 15:55:02";}s:11:" * original";a:6:{s:2:"id";i:146;s:4:"name";s:10:"kyc_wallet";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-10 12:30:30";s:10:"updated_at";s:19:"2025-04-10 15:55:02";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:146;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:147;s:4:"name";s:15:"merchant_system";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-12 11:48:35";s:10:"updated_at";s:19:"2025-04-17 09:48:00";}s:11:" * original";a:6:{s:2:"id";i:147;s:4:"name";s:15:"merchant_system";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-12 11:48:35";s:10:"updated_at";s:19:"2025-04-17 09:48:00";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:147;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:148;s:4:"name";s:12:"agent_system";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-12 12:20:27";s:10:"updated_at";s:19:"2025-04-12 16:55:13";}s:11:" * original";a:6:{s:2:"id";i:148;s:4:"name";s:12:"agent_system";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-12 12:20:27";s:10:"updated_at";s:19:"2025-04-12 16:55:13";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:148;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:149;s:4:"name";s:14:"preloader_logo";s:3:"val";s:64:"global/uploads/global/uploads/settings//73mSobAHHXAbcZhceR05.png";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2025-04-15 16:40:25";s:10:"updated_at";s:19:"2025-06-19 16:22:00";}s:11:" * original";a:6:{s:2:"id";i:149;s:4:"name";s:14:"preloader_logo";s:3:"val";s:64:"global/uploads/global/uploads/settings//73mSobAHHXAbcZhceR05.png";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2025-04-15 16:40:25";s:10:"updated_at";s:19:"2025-06-19 16:22:00";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:149;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:150;s:4:"name";s:21:"merchant_verification";s:3:"val";s:1:"0";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-16 11:57:19";s:10:"updated_at";s:19:"2025-04-16 12:31:15";}s:11:" * original";a:6:{s:2:"id";i:150;s:4:"name";s:21:"merchant_verification";s:3:"val";s:1:"0";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-16 11:57:19";s:10:"updated_at";s:19:"2025-04-16 12:31:15";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:150;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:151;s:4:"name";s:18:"agent_verification";s:3:"val";s:1:"0";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-16 11:57:19";s:10:"updated_at";s:19:"2025-04-16 12:31:15";}s:11:" * original";a:6:{s:2:"id";i:151;s:4:"name";s:18:"agent_verification";s:3:"val";s:1:"0";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-04-16 11:57:19";s:10:"updated_at";s:19:"2025-04-16 12:31:15";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:151;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:152;s:4:"name";s:13:"cashin_charge";s:3:"val";s:1:"1";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-17 12:12:12";s:10:"updated_at";s:19:"2025-04-17 12:12:12";}s:11:" * original";a:6:{s:2:"id";i:152;s:4:"name";s:13:"cashin_charge";s:3:"val";s:1:"1";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-17 12:12:12";s:10:"updated_at";s:19:"2025-04-17 12:12:12";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:152;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:153;s:4:"name";s:18:"cashin_charge_type";s:3:"val";s:10:"percentage";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-17 12:12:12";s:10:"updated_at";s:19:"2025-04-17 12:12:12";}s:11:" * original";a:6:{s:2:"id";i:153;s:4:"name";s:18:"cashin_charge_type";s:3:"val";s:10:"percentage";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-17 12:12:12";s:10:"updated_at";s:19:"2025-04-17 12:12:12";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:153;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:154;s:4:"name";s:23:"cashin_agent_commission";s:3:"val";s:1:"2";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-17 12:12:12";s:10:"updated_at";s:19:"2025-04-17 12:12:12";}s:11:" * original";a:6:{s:2:"id";i:154;s:4:"name";s:23:"cashin_agent_commission";s:3:"val";s:1:"2";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-17 12:12:12";s:10:"updated_at";s:19:"2025-04-17 12:12:12";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:154;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:155;s:4:"name";s:28:"cashin_agent_commission_type";s:3:"val";s:10:"percentage";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-17 12:12:12";s:10:"updated_at";s:19:"2025-04-17 12:12:12";}s:11:" * original";a:6:{s:2:"id";i:155;s:4:"name";s:28:"cashin_agent_commission_type";s:3:"val";s:10:"percentage";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-17 12:12:12";s:10:"updated_at";s:19:"2025-04-17 12:12:12";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:155;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:156;s:4:"name";s:14:"cashin_minimum";s:3:"val";s:2:"10";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-17 12:12:12";s:10:"updated_at";s:19:"2025-04-17 12:12:12";}s:11:" * original";a:6:{s:2:"id";i:156;s:4:"name";s:14:"cashin_minimum";s:3:"val";s:2:"10";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-17 12:12:12";s:10:"updated_at";s:19:"2025-04-17 12:12:12";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:156;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:157;s:4:"name";s:14:"cashin_maximum";s:3:"val";s:5:"10000";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-17 12:12:12";s:10:"updated_at";s:19:"2025-04-17 12:12:12";}s:11:" * original";a:6:{s:2:"id";i:157;s:4:"name";s:14:"cashin_maximum";s:3:"val";s:5:"10000";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-17 12:12:12";s:10:"updated_at";s:19:"2025-04-17 12:12:12";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:157;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:158;s:4:"name";s:18:"cashin_daily_limit";s:3:"val";s:4:"1000";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-17 12:12:12";s:10:"updated_at";s:19:"2025-04-17 12:52:54";}s:11:" * original";a:6:{s:2:"id";i:158;s:4:"name";s:18:"cashin_daily_limit";s:3:"val";s:4:"1000";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-17 12:12:12";s:10:"updated_at";s:19:"2025-04-17 12:52:54";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:158;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:159;s:4:"name";s:20:"cashin_monthly_limit";s:3:"val";s:4:"1000";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-17 12:12:12";s:10:"updated_at";s:19:"2025-04-17 12:12:12";}s:11:" * original";a:6:{s:2:"id";i:159;s:4:"name";s:20:"cashin_monthly_limit";s:3:"val";s:4:"1000";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-17 12:12:12";s:10:"updated_at";s:19:"2025-04-17 12:12:12";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:159;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:160;s:4:"name";s:15:"payment_minimum";s:3:"val";s:1:"1";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-27 10:05:03";s:10:"updated_at";s:19:"2025-04-27 10:05:07";}s:11:" * original";a:6:{s:2:"id";i:160;s:4:"name";s:15:"payment_minimum";s:3:"val";s:1:"1";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-27 10:05:03";s:10:"updated_at";s:19:"2025-04-27 10:05:07";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:160;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:161;s:4:"name";s:15:"payment_maximum";s:3:"val";s:4:"1000";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-27 10:05:03";s:10:"updated_at";s:19:"2025-04-27 10:05:11";}s:11:" * original";a:6:{s:2:"id";i:161;s:4:"name";s:15:"payment_maximum";s:3:"val";s:4:"1000";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-27 10:05:03";s:10:"updated_at";s:19:"2025-04-27 10:05:11";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:161;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:162;s:4:"name";s:16:"exchange_minimum";s:3:"val";s:2:"10";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-27 11:50:34";s:10:"updated_at";s:19:"2025-04-27 11:50:34";}s:11:" * original";a:6:{s:2:"id";i:162;s:4:"name";s:16:"exchange_minimum";s:3:"val";s:2:"10";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-27 11:50:34";s:10:"updated_at";s:19:"2025-04-27 11:50:34";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:162;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:163;s:4:"name";s:16:"exchange_maximum";s:3:"val";s:6:"100000";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-27 11:50:34";s:10:"updated_at";s:19:"2025-04-27 11:50:34";}s:11:" * original";a:6:{s:2:"id";i:163;s:4:"name";s:16:"exchange_maximum";s:3:"val";s:6:"100000";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-27 11:50:34";s:10:"updated_at";s:19:"2025-04-27 11:50:34";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:163;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:164;s:4:"name";s:21:"request_money_minimum";s:3:"val";s:2:"10";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-27 12:16:47";s:10:"updated_at";s:19:"2025-04-27 12:16:47";}s:11:" * original";a:6:{s:2:"id";i:164;s:4:"name";s:21:"request_money_minimum";s:3:"val";s:2:"10";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-27 12:16:47";s:10:"updated_at";s:19:"2025-04-27 12:16:47";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:164;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:165;s:4:"name";s:21:"request_money_maximum";s:3:"val";s:5:"10000";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-27 12:16:47";s:10:"updated_at";s:19:"2025-04-27 12:16:47";}s:11:" * original";a:6:{s:2:"id";i:165;s:4:"name";s:21:"request_money_maximum";s:3:"val";s:5:"10000";s:4:"type";s:6:"double";s:10:"created_at";s:19:"2025-04-27 12:16:47";s:10:"updated_at";s:19:"2025-04-27 12:16:47";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:165;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:166;s:4:"name";s:8:"purchase";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-05-20 14:21:16";s:10:"updated_at";s:19:"2025-05-20 14:21:20";}s:11:" * original";a:6:{s:2:"id";i:166;s:4:"name";s:8:"purchase";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-05-20 14:21:16";s:10:"updated_at";s:19:"2025-05-20 14:21:20";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:166;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:167;s:4:"name";s:13:"plan_purchase";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-05-20 17:32:06";s:10:"updated_at";s:19:"2025-06-02 17:35:50";}s:11:" * original";a:6:{s:2:"id";i:167;s:4:"name";s:13:"plan_purchase";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-05-20 17:32:06";s:10:"updated_at";s:19:"2025-06-02 17:35:50";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:167;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:168;s:4:"name";s:17:"kyc_plan_purchase";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-05-22 14:23:37";s:10:"updated_at";s:19:"2025-05-22 14:23:37";}s:11:" * original";a:6:{s:2:"id";i:168;s:4:"name";s:17:"kyc_plan_purchase";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-05-22 14:23:37";s:10:"updated_at";s:19:"2025-05-22 14:23:37";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:168;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:169;s:4:"name";s:14:"site_dark_logo";s:3:"val";s:64:"global/uploads/global/uploads/settings//Ci4zaUzYyqWXYDyRXwtj.png";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2025-05-26 11:01:52";s:10:"updated_at";s:19:"2025-06-19 16:22:00";}s:11:" * original";a:6:{s:2:"id";i:169;s:4:"name";s:14:"site_dark_logo";s:3:"val";s:64:"global/uploads/global/uploads/settings//Ci4zaUzYyqWXYDyRXwtj.png";s:4:"type";s:6:"string";s:10:"created_at";s:19:"2025-05-26 11:01:52";s:10:"updated_at";s:19:"2025-06-19 16:22:00";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:169;O:18:"App\Models\Setting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:170;s:4:"name";s:18:"landing_top_widget";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-06-04 10:58:43";s:10:"updated_at";s:19:"2025-06-14 11:24:30";}s:11:" * original";a:6:{s:2:"id";i:170;s:4:"name";s:18:"landing_top_widget";s:3:"val";s:1:"1";s:4:"type";s:7:"boolean";s:10:"created_at";s:19:"2025-06-04 10:58:43";s:10:"updated_at";s:19:"2025-06-14 11:24:30";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}}s:28:" * escapeWhenCastingToString";b:0;}