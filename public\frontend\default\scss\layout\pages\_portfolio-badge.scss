@use '../../utils' as *;

/*----------------------------------------*/
/* Portfolio badge styles
/*----------------------------------------*/
.portfolio-badge-grid {
    display: grid;
    gap: 30px 30px;
    align-items: center;
    grid-template-columns: repeat(auto-fit, minmax(348px, 1fr));

    @media #{$x3l,$xxl} {
        grid-template-columns: repeat(auto-fit, minmax(348px, 1fr));
    }

    @media #{$xl} {
        grid-template-columns: repeat(auto-fit, minmax(305px, 1fr));
    }

    @media #{$lg} {
        grid-template-columns: repeat(3, 1fr);
    }

    @media #{$md,$sm} {
        grid-template-columns: repeat(2, 1fr);
    }

    @media #{$xs} {
        grid-template-columns: 1fr;
    }
}

.portfolio-badge-item {
    padding: 40px 20px 75px;
    position: relative;
    border-radius: 24px;
    z-index: 1;
    background-color: var(--td-white);

    @include dark-theme {
        background-color: var(--td-black);
    }

    .portfolio-active {
        position: absolute;
        top: 0;
        inset-block-start: 23px;

        img {
            width: 30px;
            height: 30px;
        }
    }

    &:hover {
        &.badge-lock {
            &::after {
                opacity: 1;
                background-color: transparent;
                background-size: 0;
            }
        }
    }

    &.is-silver {
        box-shadow: 0px 6.629px 39.771px -11.6px #8AA8B5 inset;
    }

    &.is-silver-pro {
        box-shadow: 0px 6.629px 39.771px -11.6px #3EC5FF inset;

        &::before {
            background: linear-gradient(-185deg, rgba(62, 197, 255, 1) 0%, rgba(62, 197, 255, 0) 100%);
        }
    }

    &.is-gold {
        &::before {
            background: linear-gradient(-185deg, rgba(240, 126, 0, 1) 0%, rgba(240, 126, 0, 0) 100%);
        }
    }

    &.is-gold-pro {
        &::before {
            background: linear-gradient(-185deg, rgba(255, 43, 20, 1) 0%, rgba(255, 43, 20, 0) 100%);
        }
    }

    &.is-platinum {
        &::before {
            background: linear-gradient(-185deg, rgba(250, 40, 243, 1) 0%, rgba(250, 40, 243, 0) 100%);
        }
    }

    &.is-platinum-pro {
        &::before {
            background: linear-gradient(-185deg, rgba(158, 255, 62, 1) 0%, rgba(158, 255, 62, 0) 100%);
        }
    }

    &.badge-lock {
        &:after {
            position: absolute;
            content: "";
            height: 100%;
            width: 100%;
            top: 0;
            inset-inline-start: 0;
            background-color: rgba($black, $alpha: 0.7);
            background-image: url(../images/icons/lock.svg);
            background-repeat: no-repeat;
            border-radius: 24px;
            background-size: 70px;
            background-position: center center;
            transition: .3s;
        }
    }

    .badge-pattern {
        position: absolute;
        top: 0;
        inset-inline-start: 0;
        z-index: -1;
        width: 100%;
        height: 100%;
        opacity: 0.5;

        @include dark-theme {
            opacity: 1;
        }

        img {
            width: 100%;
            height: 100%;
            border-radius: 24px;
        }
    }

    .portfolio-badge-top {
        text-align: center;
    }

    .portfolio-info {
        .title {
            font-size: 30px;
            font-weight: 600;
            margin-bottom: 8px;

            @include dark-theme {
                background: linear-gradient(180deg, #FFF 43.1%, rgba(255, 255, 255, 0.00) 100%);
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }
        }

        .earning {
            @include dark-theme {
                color: var(--td-white);
            }
        }
    }

    .features-list {
        margin-top: 30px;

        ul {
            li {
                list-style: none;
                display: flex;
                gap: 8px 8px;

                &:not(:last-child) {
                    margin-bottom: 12px;
                }

                .text {
                    @include dark-theme {
                        color: #9A9DA7;
                    }
                }
            }
        }
    }

    .badge-icon {
        margin-bottom: 10px;

        img {
            height: 100px;

            @media #{$xxs,$sm} {
                height: 80px;
            }
        }
    }

    &::before {
        position: absolute;
        content: "";
        inset: 0;
        padding: 2px;
        background: linear-gradient(-185deg, rgba(138, 168, 181, 1) 0%, rgba(138, 168, 181, 0) 100%);
        -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        border-radius: 24px;
        z-index: -1;
    }
}