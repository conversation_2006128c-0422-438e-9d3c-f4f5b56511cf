@use '../../utils' as *;

/*----------------------------------------*/
/* Calculate earning styles
/*----------------------------------------*/
.td-calculate-earning-section {
    background: #F5F5F5;
    position: relative;
    z-index: 31;

    @include dark-theme {
        background: rgba($white, $alpha: 0.04);
    }
}

.calculate-earning-form {
    .clip-path {
        position: relative;
        padding: 1px;

        .clip-path-inner {

            position: relative;
            z-index: 3;

            &::before {
                position: absolute;
                top: 0;
                inset-inline-start: 0;
                content: "";
                background: #F8FCFF;
                width: 100%;
                height: 100%;
                clip-path: polygon(0.46% 0.179%, 99.54% 0.179%, 99.54% 0.179%, 99.605% 0.196%, 99.667% 0.243%, 99.725% 0.319%, 99.778% 0.421%, 99.825% 0.547%, 99.865% 0.693%, 99.898% 0.857%, 99.922% 1.037%, 99.937% 1.23%, 99.943% 1.434%, 99.943% 75.578%, 99.943% 75.578%, 99.941% 75.677%, 99.938% 75.775%, 99.931% 75.871%, 99.923% 75.965%, 99.912% 76.056%, 99.899% 76.145%, 99.884% 76.23%, 99.867% 76.311%, 99.847% 76.388%, 99.826% 76.461%, 99.811% 76.506%, 91.702% 99.495%, 91.702% 99.495%, 91.679% 99.555%, 91.655% 99.609%, 91.63% 99.658%, 91.604% 99.7%, 91.576% 99.736%, 91.549% 99.766%, 91.52% 99.79%, 91.491% 99.807%, 91.461% 99.817%, 91.431% 99.821%, 0.46% 99.821%, 0.46% 99.821%, 0.395% 99.804%, 0.333% 99.757%, 0.275% 99.681%, 0.222% 99.579%, 0.175% 99.453%, 0.135% 99.307%, 0.102% 99.143%, 0.078% 98.963%, 0.063% 98.77%, 0.057% 98.566%, 0.057% 1.434%, 0.057% 1.434%, 0.063% 1.23%, 0.078% 1.037%, 0.102% 0.857%, 0.135% 0.693%, 0.175% 0.547%, 0.222% 0.421%, 0.275% 0.319%, 0.333% 0.243%, 0.395% 0.196%, 0.46% 0.179%);

                @include dark-theme {
                    background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -31.74%, rgba(9, 70, 255, 0.06) 88.89%);
                }
            }


            &::after {
                position: absolute;
                top: 0;
                inset-inline-start: 0;
                content: "";
                background: var(--td-liberty-blue);
                clip-path: polygon(0.46% 0.179%, 99.54% 0.179%, 99.54% 0.179%, 99.605% 0.196%, 99.667% 0.243%, 99.725% 0.319%, 99.778% 0.421%, 99.825% 0.547%, 99.865% 0.693%, 99.898% 0.857%, 99.922% 1.037%, 99.937% 1.23%, 99.943% 1.434%, 99.943% 75.578%, 99.943% 75.578%, 99.941% 75.677%, 99.938% 75.775%, 99.931% 75.871%, 99.923% 75.965%, 99.912% 76.056%, 99.899% 76.145%, 99.884% 76.23%, 99.867% 76.311%, 99.847% 76.388%, 99.826% 76.461%, 99.811% 76.506%, 91.702% 99.495%, 91.702% 99.495%, 91.679% 99.555%, 91.655% 99.609%, 91.63% 99.658%, 91.604% 99.7%, 91.576% 99.736%, 91.549% 99.766%, 91.52% 99.79%, 91.491% 99.807%, 91.461% 99.817%, 91.431% 99.821%, 0.46% 99.821%, 0.46% 99.821%, 0.395% 99.804%, 0.333% 99.757%, 0.275% 99.681%, 0.222% 99.579%, 0.175% 99.453%, 0.135% 99.307%, 0.102% 99.143%, 0.078% 98.963%, 0.063% 98.77%, 0.057% 98.566%, 0.057% 1.434%, 0.057% 1.434%, 0.063% 1.23%, 0.078% 1.037%, 0.102% 0.857%, 0.135% 0.693%, 0.175% 0.547%, 0.222% 0.421%, 0.275% 0.319%, 0.333% 0.243%, 0.395% 0.196%, 0.46% 0.179%);
                z-index: -1;
                width: 100%;
                height: 100%;
            }
        }

        &::before {
            position: absolute;
            inset-inline-start: 0;
            top: 0;
            transition: all 0.3s;
            width: 100%;
            height: 100%;
            opacity: 0.3;
            content: "";
            clip-path: polygon(0.46% 0.179%, 99.54% 0.179%, 99.54% 0.179%, 99.605% 0.196%, 99.667% 0.243%, 99.725% 0.319%, 99.778% 0.421%, 99.825% 0.547%, 99.865% 0.693%, 99.898% 0.857%, 99.922% 1.037%, 99.937% 1.23%, 99.943% 1.434%, 99.943% 75.578%, 99.943% 75.578%, 99.941% 75.677%, 99.938% 75.775%, 99.931% 75.871%, 99.923% 75.965%, 99.912% 76.056%, 99.899% 76.145%, 99.884% 76.23%, 99.867% 76.311%, 99.847% 76.388%, 99.826% 76.461%, 99.811% 76.506%, 91.702% 99.495%, 91.702% 99.495%, 91.679% 99.555%, 91.655% 99.609%, 91.63% 99.658%, 91.604% 99.7%, 91.576% 99.736%, 91.549% 99.766%, 91.52% 99.79%, 91.491% 99.807%, 91.461% 99.817%, 91.431% 99.821%, 0.46% 99.821%, 0.46% 99.821%, 0.395% 99.804%, 0.333% 99.757%, 0.275% 99.681%, 0.222% 99.579%, 0.175% 99.453%, 0.135% 99.307%, 0.102% 99.143%, 0.078% 98.963%, 0.063% 98.77%, 0.057% 98.566%, 0.057% 1.434%, 0.057% 1.434%, 0.063% 1.23%, 0.078% 1.037%, 0.102% 0.857%, 0.135% 0.693%, 0.175% 0.547%, 0.222% 0.421%, 0.275% 0.319%, 0.333% 0.243%, 0.395% 0.196%, 0.46% 0.179%);
            border-radius: 2px;
            background: linear-gradient(90deg, rgba(71, 118, 230, 1) 0%, rgba(142, 84, 233, 1) 100%);


            @include dark-theme {
                background: linear-gradient(179deg, #0B277A 0%, #0094FF 100%);
            }
        }
    }

    .filter-contents {
        display: grid;
        align-items: center;
        width: 100%;
        gap: 20px 50px;
        padding: 50px 50px;
       grid-template-columns: 1fr 1px 1fr;

        @media #{$xs,$sm} {
            padding: 20px 20px;
            gap: 20px 20px;
        }

        @media #{$xxs} {
            grid-template-columns: 1fr;
        }

        .divider {
            width: 1px;
            height: calc(100% + 100px);
            background: #C6E7FF;
            display: inline-block;
            position: relative;
            z-index: 15;

            @include dark-theme {
                background: #0094FF;
            }

            @media #{$xs,$sm} {
                height: calc(100% + 40px);
            }

            @media #{$xxs} {
                display: none;
            }

            &::before {
                position: absolute;
                content: "";
                width: 26px;
                height: 26px;
                background-image: url(../images/icons/star-gradient.svg);
                background-repeat: no-repeat;
                background-size: cover;
                bottom: -13px;
                inset-inline-end: -13px;
            }
        }

        .select2-container--default .select2-selection {
            border-radius: 4px;
            border: 1px solid #4776E6;
            backdrop-filter: blur(10px);
            background: linear-gradient(90deg, rgba(71, 118, 230, 0.1) 40.3%, rgba(142, 84, 233, 0.1) 100%);

            @include dark-theme {
                border-color: rgba($white, $alpha: 0.1);
            }

        }

        .select2-dropdown {
            box-shadow: none;
            border: 1px solid #D1D4DA;
            background: #F8FCFF;
            backdrop-filter: blur(10px);

            @include dark-theme {
                background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -31.74%, rgba(9, 70, 255, 0.06) 88.89%);
            }
        }

        .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: #E6EFFC !important;
            color: var(--td-heading) !important;

            @include dark-theme {
                background-color: rgba(255, 255, 255, 0.1) !important;
            }
        }

        .select2-container--default .select2-results__option {
            color: var(--td-heading);
            font-weight: 600;
            font-size: 14px;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            color: var(--td-heading);
        }

        .select2-container--default.select2-container--open .select2-selection {
            border-color: #4776E6 !important;
        }

        .select2-container--default.select2-container--focus .select2-selection {
            border-color: #4776E6 !important;
        }
    }

    .Revenue-content {
        border-top: 1px solid #C6E7FF;
        padding: 30px 50px;
        text-align: center;
        position: relative;
        z-index: 2;

        @include dark-theme {
            border-color: #0094FF;
        }

        .title {
            font-size: 16px;
            margin-bottom: 10px;
            background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .currency {
            font-size: 24px;
            font-weight: 700;
        }
    }
}