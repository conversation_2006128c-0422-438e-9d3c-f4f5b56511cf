<script>

    $(document).on('click', '#theme-toggle', function (e) {
        // var theme = ''
        if ($('body').hasClass('dark-theme')) {
            var theme = 'dark'
            $('body').addClass('dark-theme');
            $('.logo-white').show();
            $('.logo-black').hide();
            $('.tradingview-widget-container iframe[src*="dark"]').css('height', '50px')
            $('.tradingview-widget-container iframe[src*="light"]').css('height', 0)
        } else {
            var theme = 'light'
            $('body').removeClass('dark-theme');
            $('.logo-black').show();
            $('.logo-white').hide();
            $('.tradingview-widget-container iframe[src*="dark"]').css('height', 0)
            $('.tradingview-widget-container iframe[src*="light"]').css('height', '50px')
        }


        localStorage.setItem("theme", theme);
        $.ajax({
            url: "{{ route('theme-mode') }}",
            type: 'GET',
            data: {
                theme: theme
            },
            success: function (response) {

            }, error: () => {
                $('.logo-white, .logo-black,.tradingview-widget-container iframe, body.dark-theme').toggle();
            }
        });
    })
</script>