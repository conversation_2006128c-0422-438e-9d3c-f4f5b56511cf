<?php $__env->startSection('title'); ?>
    <?php echo e(__('Withdraw Account')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
<div class="col-xxl-12">

    <!-- Page title wrapper -->
    <div class="page-title-wrapper mb-16">
        <div class="page-title-contents">
            <h3 class="page-title"><?php echo e(__('Withdraw Account')); ?></h3>
        </div>
    </div>
    <?php echo $__env->make('frontend::user.withdraw._topbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <!-- Page title wrapper -->
</div>
<div class="col-xxl-12">
    <form method="GET" action="<?php echo e(route('user.withdraw.account.index')); ?>">
        <div class="filter-bar">
            <!-- Method Name Filter -->
            <div class="filter-item">
                <div class="clip-path-inner">
                    <div class="td-form-group has-multiple">
                        <div class="input-field-inner">
                            <div class="input-field">
                                <input type="text" name="keyword" value="<?php echo e(request('keyword')); ?>" placeholder="Method Name" class="input-box">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Per Page Filter -->
            <div class="filter-item">
                <div class="clip-path-inner">
                    <div class="td-form-group has-multiple">
                        <div class="input-field-inner">
                            <div class="input-field">
                                <select name="perPage" class="defaultselect2 select2-hidden-accessible" data-select2-id="perPage">
                                    <option value="10" <?php echo e(request('perPage') == '10' ? 'selected' : ''); ?>>10</option>
                                    <option value="25" <?php echo e(request('perPage') == '25' ? 'selected' : ''); ?>>25</option>
                                    <option value="50" <?php echo e(request('perPage') == '50' ? 'selected' : ''); ?>>50</option>
                                    <option value="100" <?php echo e(request('perPage') == '100' ? 'selected' : ''); ?>>100</option>
                                    <option value="200" <?php echo e(request('perPage') == '200' ? 'selected' : ''); ?>>200</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search Button -->
            <div class="filter-item">
                <button type="submit" class="td-btn btn-sm btn-clip grd-outline-fill-btn primary-btn">
                    <span class="inner-btn">
                        <span class="btn-text"><?php echo e(__('Search')); ?></span>
                     </span>
                </button>
            </div>

            <!-- Add New Withdraw Account Button -->
            <div class="filter-item">
                <a href="<?php echo e(route('user.withdraw.account.create')); ?>" class="td-btn btn-sm btn-clip grd-fill-btn-primary">
                    <i class="hugeicons--plus-sign"></i> <?php echo e(__('Add New Account')); ?>

                </a>
            </div>
        </div>
    </form>

    <div class="recent-history-table">
        <div class="table-container table-responsive">
            <table class="td-table recent-table">
                <thead>
                    <tr>
                        <th><?php echo e(__('SL NO')); ?></th>
                        <th><?php echo e(__('Account')); ?></th>
                        <th><?php echo e(__('Action')); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $withdrawAccounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td><?php echo e($key + $withdrawAccounts->firstItem()); ?></td>
                            <td><?php echo e($account->method_name); ?></td>
                            <td class="action-btns">
                                <a href="<?php echo e(route('user.withdraw.account.edit', encrypt($account->id))); ?>"
                                   class="td-btn btn-xs success-btn radius-30" data-bs-toggle="tooltip" title="Edit">
                                    <?php echo e(__('Edit')); ?>

                                </a>
                                <a href="javascript:;" class="td-btn btn-xs outline-danger-btn radius-30"
                                   data-routeurl="<?php echo e(route('user.withdraw.account.delete', encrypt($account->id))); ?>"
                                   data-bs-toggle="tooltip" title="Delete">
                                    <?php echo e(__('Delete')); ?>

                                </a>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="3" class="text-center">
                                <?php if (isset($component)) { $__componentOriginale68a44eee86a1e00bdfa66b1bce4fc09 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale68a44eee86a1e00bdfa66b1bce4fc09 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.no-data-found','data' => ['class' => 'mt-10','module' => ''.e(__('Withdraw Account')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('no-data-found'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mt-10','module' => ''.e(__('Withdraw Account')).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale68a44eee86a1e00bdfa66b1bce4fc09)): ?>
<?php $attributes = $__attributesOriginale68a44eee86a1e00bdfa66b1bce4fc09; ?>
<?php unset($__attributesOriginale68a44eee86a1e00bdfa66b1bce4fc09); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale68a44eee86a1e00bdfa66b1bce4fc09)): ?>
<?php $component = $__componentOriginale68a44eee86a1e00bdfa66b1bce4fc09; ?>
<?php unset($__componentOriginale68a44eee86a1e00bdfa66b1bce4fc09); ?>
<?php endif; ?>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="td-pagination mt-30 d-flex justify-content-end">
            <?php echo e($withdrawAccounts->links()); ?>

        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal default-model fade" id="deleteInfo" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title fs-5"><?php echo e(__('Delete Withdraw Account')); ?></h3>
            </div>
            <div class="modal-body">
                <form action="" method="POST" id="deleteAccountFrom">
                    <?php echo csrf_field(); ?>
                    <div class="create-ticket-from text-center">
                        <div class="info-icon mb-3">
                            <iconify-icon icon="tabler:alert-triangle" width="40" height="40"></iconify-icon>
                        </div>
                        <h4><?php echo e(__('Are you sure?')); ?></h4>
                        <p><?php echo e(__('You want to delete this account?')); ?></p>
                    </div>
                    <div class="modal-buttons mt-3 d-flex gap-3 flex-wrap justify-content-end">
                        <button type="button" class="td-btn btn-clip white-btn-12 back-btn" data-bs-dismiss="modal" aria-label="Close">
                            <span class="btn-text"><?php echo e(__('Cancel')); ?></span>
                        </button>
                        <button type="submit" class="td-btn btn-clip grd-fill-btn-primary btn-m-w">
                            <span class="btn-text"><?php echo e(__('Confirm Delete')); ?></span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    <script>
        "use strict";
        $(".deleteData").on('click', function() {
            var routeurl = $(this).data('routeurl');
            $('#deleteAccountFrom').attr('action', routeurl);
            $("#deleteInfo").modal('show');
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('frontend::layouts.user', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\app\Providers/../../resources/views/frontend/default/user/withdraw/account/index.blade.php ENDPATH**/ ?>