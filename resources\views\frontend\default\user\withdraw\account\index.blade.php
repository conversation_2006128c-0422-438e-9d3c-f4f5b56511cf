@extends('frontend::layouts.user')
@section('title')
    {{ __('Withdraw Account') }}
@endsection
@section('content')
<div class="col-xxl-12">

    <!-- Page title wrapper -->
    <div class="page-title-wrapper mb-16">
        <div class="page-title-contents">
            <h3 class="page-title">{{ __('Withdraw Account') }}</h3>
        </div>
    </div>
    @include('frontend::user.withdraw._topbar')
    <!-- Page title wrapper -->
</div>
<div class="col-xxl-12">
    <form method="GET" action="{{ route('user.withdraw.account.index') }}">
        <div class="filter-bar">
            <!-- Method Name Filter -->
            <div class="filter-item">
                <div class="clip-path-inner">
                    <div class="td-form-group has-multiple">
                        <div class="input-field-inner">
                            <div class="input-field">
                                <input type="text" name="keyword" value="{{ request('keyword') }}" placeholder="Method Name" class="input-box">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Per Page Filter -->
            <div class="filter-item">
                <div class="clip-path-inner">
                    <div class="td-form-group has-multiple">
                        <div class="input-field-inner">
                            <div class="input-field">
                                <select name="perPage" class="defaultselect2 select2-hidden-accessible" data-select2-id="perPage">
                                    <option value="10" {{ request('perPage') == '10' ? 'selected' : '' }}>10</option>
                                    <option value="25" {{ request('perPage') == '25' ? 'selected' : '' }}>25</option>
                                    <option value="50" {{ request('perPage') == '50' ? 'selected' : '' }}>50</option>
                                    <option value="100" {{ request('perPage') == '100' ? 'selected' : '' }}>100</option>
                                    <option value="200" {{ request('perPage') == '200' ? 'selected' : '' }}>200</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search Button -->
            <div class="filter-item">
                <button type="submit" class="td-btn btn-sm btn-clip grd-outline-fill-btn primary-btn">
                    <span class="inner-btn">
                        <span class="btn-text">{{ __('Search') }}</span>
                     </span>
                </button>
            </div>

            <!-- Add New Withdraw Account Button -->
            <div class="filter-item">
                <a href="{{ route('user.withdraw.account.create') }}" class="td-btn btn-sm btn-clip grd-fill-btn-primary">
                    <i class="hugeicons--plus-sign"></i> {{ __('Add New Account') }}
                </a>
            </div>
        </div>
    </form>

    <div class="recent-history-table">
        <div class="table-container table-responsive">
            <table class="td-table recent-table">
                <thead>
                    <tr>
                        <th>{{ __('SL NO') }}</th>
                        <th>{{ __('Account') }}</th>
                        <th>{{ __('Action') }}</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($withdrawAccounts as $key => $account)
                        <tr>
                            <td>{{ $key + $withdrawAccounts->firstItem() }}</td>
                            <td>{{ $account->method_name }}</td>
                            <td class="action-btns">
                                <a href="{{ route('user.withdraw.account.edit', encrypt($account->id)) }}"
                                   class="td-btn btn-xs success-btn radius-30" data-bs-toggle="tooltip" title="Edit">
                                    {{ __('Edit') }}
                                </a>
                                <a href="javascript:;" class="td-btn btn-xs outline-danger-btn radius-30"
                                   data-routeurl="{{ route('user.withdraw.account.delete', encrypt($account->id)) }}"
                                   data-bs-toggle="tooltip" title="Delete">
                                    {{ __('Delete') }}
                                </a>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="3" class="text-center">
                                <x-no-data-found class="mt-10" module="{{ __('Withdraw Account') }}" />
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="td-pagination mt-30 d-flex justify-content-end">
            {{ $withdrawAccounts->links() }}
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal default-model fade" id="deleteInfo" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title fs-5">{{ __('Delete Withdraw Account') }}</h3>
            </div>
            <div class="modal-body">
                <form action="" method="POST" id="deleteAccountFrom">
                    @csrf
                    <div class="create-ticket-from text-center">
                        <div class="info-icon mb-3">
                            <iconify-icon icon="tabler:alert-triangle" width="40" height="40"></iconify-icon>
                        </div>
                        <h4>{{ __('Are you sure?') }}</h4>
                        <p>{{ __('You want to delete this account?') }}</p>
                    </div>
                    <div class="modal-buttons mt-3 d-flex gap-3 flex-wrap justify-content-end">
                        <button type="button" class="td-btn btn-clip white-btn-12 back-btn" data-bs-dismiss="modal" aria-label="Close">
                            <span class="btn-text">{{ __('Cancel') }}</span>
                        </button>
                        <button type="submit" class="td-btn btn-clip grd-fill-btn-primary btn-m-w">
                            <span class="btn-text">{{ __('Confirm Delete') }}</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@endsection

@push('js')
    <script>
        "use strict";
        $(".deleteData").on('click', function() {
            var routeurl = $(this).data('routeurl');
            $('#deleteAccountFrom').attr('action', routeurl);
            $("#deleteInfo").modal('show');
        });
    </script>
@endpush
