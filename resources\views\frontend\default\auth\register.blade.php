@extends('frontend::layouts.auth')

@section('title')
    {{ __('Register') }}
@endsection
@push('style')
@endpush

@section('content')
<section class="td-authentication-section">
    <div class="container">
        <div class="auth-main-box">
            <div class="auth-top-wrapper">
                <div class="auth-logo">
                    <a href="{{ route('home') }}">
                        @include('frontend::auth.logo')
                    </a>
                </div>
                <div class="auth-intro-contents">
                    <h4 class="title">{{ $data['title'] }}</h4>
                </div>
            </div>

            <div class="auth-from-box">
                <form id="register-form" method="POST" action="{{ route('register') }}" novalidate>
                    @csrf

                    <div class="auth-form-group">
                        <!-- First Name -->
                        <div class="td-form-group has-left-icon @error('first_name') is-invalid @enderror">
                            <div class="input-field">
                                <input type="text" name="first_name" class="form-control" id="f-name"
                                       placeholder="First name" required value="{{ old('first_name') }}">
                                <span class="input-icon">
                                    <iconify-icon icon="tabler:user"></iconify-icon>
                                </span>
                            </div>
                            @error('first_name')
                                <p class="feedback-invalid">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Last Name -->
                        <div class="td-form-group has-left-icon @error('last_name') is-invalid @enderror">
                            <div class="input-field">
                                <input type="text" name="last_name" class="form-control" id="l-name"
                                       placeholder="Last name" required value="{{ old('last_name') }}">
                                <span class="input-icon">
                                    <iconify-icon icon="tabler:user"></iconify-icon>
                                </span>
                            </div>
                            @error('last_name')
                                <p class="feedback-invalid">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Email-->
                        <div class="td-form-group has-left-icon @error('email') is-invalid @enderror">
                            <div class="input-field">
                                <input type="text" name="email" class="form-control" id="email"
                                       placeholder="Email" required value="{{ old('email') }}">
                                <span class="input-icon">
                                    <iconify-icon icon="tabler:mail"></iconify-icon>
                                </span>
                            </div>
                            @error('email')
                                <p class="feedback-invalid">{{ $message }}</p>
                            @enderror
                        </div>
                        <!-- Username -->
                        @if (getPageSetting('username_show'))
                        <div class="td-form-group has-left-icon @error('username') is-invalid @enderror">
                            <div class="input-field">
                                <input type="text" name="username" class="form-control" id="username"
                                       placeholder="Username" {{ getPageSetting('username_validation') ? 'required' : '' }} value="{{ old('username') }}">
                                <span class="input-icon">
                                    <iconify-icon icon="tabler:at"></iconify-icon>
                                </span>
                            </div>
                            @error('username')
                                <p class="feedback-invalid">{{ $message }}</p>
                            @enderror
                        </div>
                        @endif

                        <!-- Country -->
                        @if (getPageSetting('country_show'))
                            <div class="td-form-group has-left-icon @error('country') is-invalid @enderror">
                                <div class="input-field">
                                    <select name="country" class="form-select select2Icons form-control " id="countrySelect" @required(getPageSetting('country_validation'))>
                                        <option value="" disabled>{{ __('Select Country') }}</option>
                                        @foreach (getCountries() as $country)
                                            <option
                                                value="{{ $country['name'] }}:{{ $country['dial_code'] }}"
                                                data-image="{{ $country['flag'] }}"
                                                data-code="{{ $country['dial_code'] }}"
                                                @selected(old('country', data_get($location, 'name') . ':' . data_get($location, 'dial_code')) == $country['name'] . ':' . $country['dial_code'])>
                                                {{ $country['name'] }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <span class="input-icon">
                                        <iconify-icon icon="tabler:world"></iconify-icon>
                                    </span>
                                </div>
                                @error('country')
                                    <p class="feedback-invalid">{{ $message }}</p>
                                @enderror
                            </div>
                        @endif

                        <!-- Phone Number -->
                        @if (getPageSetting('phone_show'))
                            <div class="td-form-group has-left-icon @error('phone') is-invalid @enderror">
                                <div class="input-field input-group">
                                    <span class="input-group-text" id="country-dial-code">
                                        +{{ old('country_code', '880') }}
                                    </span>
                                    <input type="text" name="phone" class="form-control" id="phone"
                                           placeholder="Your phone number" @required(getPageSetting('phone_validation')) value="{{ old('phone') }}">
                                    <span class="input-icon">
                                        <iconify-icon icon="tabler:phone-call"></iconify-icon>
                                    </span>
                                </div>
                                @error('phone')
                                    <p class="feedback-invalid">{{ $message }}</p>
                                @enderror
                            </div>
                        @endif

                        <!-- Referral Code -->
                        @if (getPageSetting('referral_code_show'))
                            <div class="td-form-group has-left-icon @error('invite') is-invalid @enderror">
                                <div class="input-field">
                                    <input type="text" name="invite" class="form-control" id="referral-code"
                                           placeholder="Referral Code" value="{{ request('invite') }}" 
                                           @if (getPageSetting('referral_code_validation')) required @endif>
                                    <span class="input-icon">
                                        <iconify-icon icon="tabler:user-plus"></iconify-icon>
                                    </span>
                                </div>
                                @error('invite')
                                    <p class="feedback-invalid">{{ $message }}</p>
                                @enderror
                            </div>
                        @else
                            <input type="hidden" name="invite" value="{{ request('invite') }}">
                        @endif

                        <!-- Gender -->
                        @if (getPageSetting('gender_show'))
                            <div class="td-form-group has-left-icon @error('gender') is-invalid @enderror">
                                <div class="input-field">
                                    <select name="gender" class="form-select defaultselect2" @required(getPageSetting('gender_validation'))>
                                        <option value="" disabled selected>Select Gender</option>
                                        <option value="male" @selected(old('gender') == 'male')>Male</option>
                                        <option value="female" @selected(old('gender') == 'female')>Female</option>
                                        <option value="other" @selected(old('gender') == 'other')>Other</option>
                                    </select>
                                    <span class="input-icon">
                                        <iconify-icon icon="tabler:gender-male"></iconify-icon>
                                    </span>
                                </div>
                                @error('gender')
                                    <p class="feedback-invalid">{{ $message }}</p>
                                @enderror
                            </div>
                        @endif

                        <!-- Password -->
                        <div class="td-form-group has-left-icon has-right-icon @error('password') is-invalid @enderror">
                            <div class="input-field">
                                <input type="password" name="password" class="form-control password-input"
                                       id="password" placeholder="Password" required>
                                <span class="input-icon">
                                    <iconify-icon icon="tabler:lock"></iconify-icon>
                                </span>
                                <span class="input-icon eyeicon">
                                    <img id="eye-icon-img" class="eye-img" src="{{ frontendAsset('images/icons/eye.svg') }}" alt="eye">
                                </span>
                            </div>
                            @error('password')
                                <p class="feedback-invalid">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Confirm Password -->
                        <div class="td-form-group has-left-icon has-right-icon @error('password_confirmation') is-invalid @enderror">
                            <div class="input-field">
                                <input type="password" name="password_confirmation" class="form-control password-input"
                                       id="password_confirmation" placeholder="Confirm Password" required>
                                <span class="input-icon">
                                    <iconify-icon icon="tabler:lock"></iconify-icon>
                                </span>
                                <span class="input-icon eyeicon">
                                    <img id="eye-icon-img-confirm" class="eye-img" src="{{ frontendAsset('images/icons/eye.svg') }}" alt="eye">
                                </span>
                            </div>
                            @error('password_confirmation')
                                <p class="feedback-invalid">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                    <div class="input-attention-text text-end mt-8">
                        <p class="input-attention">{{ __('Minimum length is 8 characters.') }}</p>
                     </div>

                    <!-- Terms & Conditions -->
                    <div class="auth-login-option mt-10 mb-30">
                        <div class="animate-custom">
                            <input class="inp-cbx" id="auth_trams" type="checkbox" name="i_agree" style="display: none;" required>
                            <label class="cbx" for="auth_trams">
                                <span>
                                    <svg width="12px" height="9px" viewBox="0 0 12 9">
                                        <polyline points="1 5 4 8 11 1"></polyline>
                                    </svg>
                                </span>
                                <span>{{ __('I agree to the') }} <a class="td-underline-btn" href="{{ url('page/terms-conditions') }}">{{ __('Terms & Conditions') }}</a></span>
                            </label>
                            @error('i_agree')
                                <p class="feedback-invalid">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="auth-from-btn-wrap">
                        <button class="td-btn btn-clip grd-fill-btn-primary w-100 radius-30" type="submit">{{ __('Register') }}</button>
                    </div>
                </form>
            </div>

            <!-- Bottom Link -->
            <div class="auth-from-bottom-contents mt-10">
                <div class="have-auth-accounts text-center">
                    <p class="description">{{ __('Already have an account?') }}
                        <a class="td-underline-btn" href="{{ route('login') }}">{{ __('Sign In') }}</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('script')
    <script>
        $(document).on('change', '#countrySelect', function(e) {
            var country = $(this).val();
            $('#country-dial-code').html(country.split(":")[1])
        });

        $('#countrySelect').trigger('change');
    </script>

<script>
    document.getElementById('eye-icon-img').addEventListener('click', function () {
        const input = document.getElementById('password');
        togglePasswordVisibility(input, this);
    });

    document.getElementById('eye-icon-img-confirm').addEventListener('click', function () {
        const input = document.getElementById('password_confirmation');
        togglePasswordVisibility(input, this);
    });

    function togglePasswordVisibility(input, iconElement) {
        const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
        input.setAttribute('type', type);
        iconElement.src = type != 'password'
            ? "{{ frontendAsset('images/icons/eye-open.svg') }}"
            : "{{ frontendAsset('images/icons/eye.svg') }}";
    }
</script>
@endpush
