@extends('frontend::layouts.auth')

@section('title')
    {{ __('Login') }}
@endsection

@section('content')
<section class="td-authentication-section">
    <div class="container">
        <div class="auth-main-box">
            <div class="auth-top-wrapper">
                <div class="auth-logo">
                    <a href="{{ route('home') }}">
                        @include('frontend::auth.logo')
                    </a>
                </div>
                <div class="auth-intro-contents">
                    <h4 class="title">{{ $data['title'] }}</h4>
                </div>
            </div>
            <div class="auth-from-box">
                <form id="sign-in-form" method="POST" action="{{ route('login') }}">
                    @csrf
                    <div class="auth-form-group">
                        <div class="td-form-group has-left-icon @error('email') is-invalid @enderror">
                            <div class="input-field">
                                <input type="email" name="email" class="form-control" id="email" placeholder="Email address" required value="{{ old('email') }}">
                                <span class="input-icon">
                                    <iconify-icon icon="tabler:mail"></iconify-icon>
                                </span>
                            </div>
                            @error('email')
                                <p class="feedback-invalid">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="td-form-group has-left-icon has-right-icon @error('password') is-invalid @enderror">
                            <div class="input-field">
                                <input type="password" name="password" class="form-control password-input" id="password" placeholder="Password" required>
                                <span class="input-icon">
                                    <iconify-icon icon="tabler:lock"></iconify-icon>
                                </span>
                                <span class="input-icon eyeicon">
                                    <img id="eye-icon-img" class="eye-img" src="{{ frontendAsset('images/icons/eye.svg') }}" alt="eye">
                                </span>
                            </div>
                            @error('password')
                                <p class="feedback-invalid">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <div class="auth-login-option mt-10 mb-30">
                        <div class="animate-custom">
                            <input class="inp-cbx" id="auth_remind" name="remember" type="checkbox" style="display: none;">
                            <label class="cbx" for="auth_remind">
                                <span>
                                    <svg width="12px" height="9px" viewBox="0 0 12 9">
                                        <polyline points="1 5 4 8 11 1"></polyline>
                                    </svg>
                                </span>
                                <span>Remember me</span>
                            </label>
                        </div>
                        <div class="forget-content">
                            <span><a class="td-underline-btn" href="{{ route('password.request') }}">Forget Password</a></span>
                        </div>
                    </div>

                    <div class="auth-from-btn-wrap">
                        <button class="td-btn btn-clip grd-fill-btn-primary w-100 radius-30" type="submit">Sign In</button>
                    </div>
                </form>
            </div>

            <div class="auth-from-bottom-contents mt-10">
                <div class="have-auth-accounts text-center">
                    <p class="description">Don’t have an account? 
                        <a class="td-underline-btn" href="{{ route('register') }}">Sign Up</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
@push('script')
<script>
   function togglePasswordVisibility(event) {
        const eyeIconSpan = event.currentTarget;
        const passwordInput = eyeIconSpan.closest('.input-field').querySelector('input');
        // For the image version
        const eyeIconImg = eyeIconSpan.querySelector('img');
        const eyeIconI = eyeIconSpan.querySelector('i');

        // Toggle logic for the image version
        if (passwordInput.type === "password") {
            $(passwordInput).attr('type', 'text');
            if (eyeIconImg) {
                eyeIconImg.src = "{{ frontendAsset('/images/icons/eye-open.svg') }}";
            }
            if (eyeIconI) {
                eyeIconI.classList.replace('fa-eye', 'fa-eye-slash');
            }
        } else {
            passwordInput.type = "password";
            if (eyeIconImg) {
                eyeIconImg.src = "{{ frontendAsset('/images/icons/eye.svg') }}";
            }
            if (eyeIconI) {
                eyeIconI.classList.replace('fa-eye-slash', 'fa-eye');
            }
        }
    }

    // Attach event listeners to all eye icon spans
    document.querySelectorAll('.eyeicon').forEach(function (eyeIconSpan) {
        eyeIconSpan.addEventListener('click', togglePasswordVisibility);
    });
</script>
@endpush
