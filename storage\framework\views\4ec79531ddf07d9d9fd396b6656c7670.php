<?php $__env->startSection('title'); ?>
    <?php echo e(__('Login')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<section class="td-authentication-section">
    <div class="container">
        <div class="auth-main-box">
            <div class="auth-top-wrapper">
                <div class="auth-logo">
                    <a href="<?php echo e(route('home')); ?>">
                        <?php echo $__env->make('frontend::auth.logo', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </a>
                </div>
                <div class="auth-intro-contents">
                    <h4 class="title"><?php echo e($data['title']); ?></h4>
                </div>
            </div>
            <div class="auth-from-box">
                <form id="sign-in-form" method="POST" action="<?php echo e(route('login')); ?>">
                    <?php echo csrf_field(); ?>
                    <div class="auth-form-group">
                        <div class="td-form-group has-left-icon <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <div class="input-field">
                                <input type="email" name="email" class="form-control" id="email" placeholder="Email address" required value="<?php echo e(old('email')); ?>">
                                <span class="input-icon">
                                    <iconify-icon icon="tabler:mail"></iconify-icon>
                                </span>
                            </div>
                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="feedback-invalid"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="td-form-group has-left-icon has-right-icon <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <div class="input-field">
                                <input type="password" name="password" class="form-control password-input" id="password" placeholder="Password" required>
                                <span class="input-icon">
                                    <iconify-icon icon="tabler:lock"></iconify-icon>
                                </span>
                                <span class="input-icon eyeicon">
                                    <img id="eye-icon-img" class="eye-img" src="<?php echo e(frontendAsset('images/icons/eye.svg')); ?>" alt="eye">
                                </span>
                            </div>
                            <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="feedback-invalid"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <div class="auth-login-option mt-10 mb-30">
                        <div class="animate-custom">
                            <input class="inp-cbx" id="auth_remind" name="remember" type="checkbox" style="display: none;">
                            <label class="cbx" for="auth_remind">
                                <span>
                                    <svg width="12px" height="9px" viewBox="0 0 12 9">
                                        <polyline points="1 5 4 8 11 1"></polyline>
                                    </svg>
                                </span>
                                <span>Remember me</span>
                            </label>
                        </div>
                        <div class="forget-content">
                            <span><a class="td-underline-btn" href="<?php echo e(route('password.request')); ?>">Forget Password</a></span>
                        </div>
                    </div>

                    <div class="auth-from-btn-wrap">
                        <button class="td-btn btn-clip grd-fill-btn-primary w-100 radius-30" type="submit">Sign In</button>
                    </div>
                </form>
            </div>

            <div class="auth-from-bottom-contents mt-10">
                <div class="have-auth-accounts text-center">
                    <p class="description">Don’t have an account? 
                        <a class="td-underline-btn" href="<?php echo e(route('register')); ?>">Sign Up</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('script'); ?>
<script>
   function togglePasswordVisibility(event) {
        const eyeIconSpan = event.currentTarget;
        const passwordInput = eyeIconSpan.closest('.input-field').querySelector('input');
        // For the image version
        const eyeIconImg = eyeIconSpan.querySelector('img');
        const eyeIconI = eyeIconSpan.querySelector('i');

        // Toggle logic for the image version
        if (passwordInput.type === "password") {
            $(passwordInput).attr('type', 'text');
            if (eyeIconImg) {
                eyeIconImg.src = "<?php echo e(frontendAsset('/images/icons/eye-open.svg')); ?>";
            }
            if (eyeIconI) {
                eyeIconI.classList.replace('fa-eye', 'fa-eye-slash');
            }
        } else {
            passwordInput.type = "password";
            if (eyeIconImg) {
                eyeIconImg.src = "<?php echo e(frontendAsset('/images/icons/eye.svg')); ?>";
            }
            if (eyeIconI) {
                eyeIconI.classList.replace('fa-eye-slash', 'fa-eye');
            }
        }
    }

    // Attach event listeners to all eye icon spans
    document.querySelectorAll('.eyeicon').forEach(function (eyeIconSpan) {
        eyeIconSpan.addEventListener('click', togglePasswordVisibility);
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('frontend::layouts.auth', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\app\Providers/../../resources/views/frontend/default/auth/login.blade.php ENDPATH**/ ?>