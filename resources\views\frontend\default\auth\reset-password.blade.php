@extends('frontend::layouts.auth')

@section('title')
    {{ __('Reset Password') }}
@endsection

@section('content')
<section class="td-authentication-section">
    <div class="container">
        <div class="auth-main-box">
            <div class="auth-top-wrapper">
                <div class="auth-logo">
                    <a href="{{ route('home') }}">
                        @include('frontend::auth.logo')
                    </a>
                </div>
                <div class="auth-intro-contents">
                    <h4 class="title">{{ $data['title'] }}</h4>
                </div>
            </div>

            <div class="auth-from-box">
                <form action="{{ route('password.store') }}" method="POST">
                    @csrf
                    <input type="hidden" name="token" value="{{ request('token') }}">

                    <!-- Email Address -->
                    <div class="td-form-group has-left-icon mb-20">
                        <div class="input-field">
                            <input type="email" name="email" class="form-control" id="email"
                                   placeholder="Email Address" value="{{ old('email', request('email')) }}" readonly>
                            <span class="input-icon">
                                <iconify-icon icon="tabler:mail"></iconify-icon>
                            </span>
                        </div>
                        @error('email')
                            <p class="feedback-invalid">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Password -->
                    <div class="td-form-group has-left-icon has-right-icon mb-20">
                        <div class="input-field">
                            <input type="password" name="password" class="form-control password-input"
                                   id="password" placeholder="Password" required>
                            <span class="input-icon">
                                <iconify-icon icon="tabler:lock"></iconify-icon>
                            </span>
                            <span class="input-icon eyeicon">
                                <img id="eye-icon-img" class="eye-img" src="{{ frontendAsset('images/icons/eye.svg') }}" alt="eye">
                            </span>
                        </div>
                        @error('password')
                            <p class="feedback-invalid">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Confirm Password -->
                    <div class="td-form-group has-left-icon has-right-icon mb-20">
                        <div class="input-field">
                            <input type="password" name="password_confirmation" class="form-control password-input"
                                   id="password_confirmation" placeholder="Confirm Password" required>
                            <span class="input-icon">
                                <iconify-icon icon="tabler:lock"></iconify-icon>
                            </span>
                            <span class="input-icon eyeicon">
                                <img id="eye-icon-img-confirm" class="eye-img" src="{{ frontendAsset('images/icons/eye.svg') }}" alt="eye">
                            </span>
                        </div>
                        @error('password_confirmation')
                            <p class="feedback-invalid">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Submit Button -->
                    <div class="auth-from-btn-wrap mt-30">
                        <button class="td-btn btn-clip grd-fill-btn-primary w-100 radius-30" type="submit">
                            {{ __('Reset Password') }}
                        </button>
                    </div>
                </form>
            </div>

            <div class="auth-from-bottom-contents mt-10 text-center">
                <p class="description">Remembered your password?
                    <a class="td-underline-btn" href="{{ route('login') }}">Sign In</a>
                </p>
            </div>
        </div>
    </div>
</section>
@endsection
@push('script')
<script>
    document.getElementById('eye-icon-img').addEventListener('click', function () {
        const input = document.getElementById('password');
        togglePasswordVisibility(input, this);
    });

    document.getElementById('eye-icon-img-confirm').addEventListener('click', function () {
        const input = document.getElementById('password_confirmation');
        togglePasswordVisibility(input, this);
    });

    function togglePasswordVisibility(input, iconElement) {
        const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
        input.setAttribute('type', type);
        iconElement.src = type != 'password'
            ? "{{ frontendAsset('images/icons/eye-open.svg') }}"
            : "{{ frontendAsset('images/icons/eye.svg') }}";
    }
</script>
@endpush