@use '../../utils' as *;

/*----------------------------------------*/
/* Auth styles
/*----------------------------------------*/
.td-authentication-section {
    min-height: 100vh;
    display: grid;
    place-items: center;
    padding: 50px 0;

    @media #{$xs,$sm,$md} {
        padding: 100px 0 50px;
    }
}

.auth-top-wrapper {
    margin-bottom: 25px;
    text-align: center;
}

.auth-main-box {
    width: 565px;
    margin: 0 auto;
    border-radius: 16px;
    background-color: var(--td-white);
    backdrop-filter: blur(10px);
    padding: 50px;

    @include dark-theme {
        background-color: #230C6B;
    }

    @media #{$xs,$sm} {
        width: 100%;
    }

    @media #{$xs} {
        padding: 30px 30px;
    }
}

.auth-logo {
    margin-bottom: 25px;
}

.auth-intro-contents {
    .title {
        font-size: 24px;

        @media #{$xs} {
            font-size: 20px;
        }
    }

    .description {
        margin-top: 12px;
    }
}

.auth-login-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 12px;
}

.auth-form-group {
    border-radius: 8px;
    border: 1px solid rgba($heading, $alpha: 0.16);

    @include dark-theme {
        border-color: rgba($white, $alpha: 0.1);
    }

    .td-form-group {
        position: relative;

        &:not(:last-child) {
            border-bottom: 1px solid #e1e1e1;

            @include dark-theme {
                border-color: rgba($white, $alpha: 0.1);
            }
        }

        .input-field {
            .form-control {
                border: 0;
                height: 60px;
                border-radius: 0px;
                font-size: 16px;

                @include td-placeholder {
                    font-size: 16px;
                }

                @include dark-theme {
                    background-color: transparent;

                    @include td-placeholder {
                        color: #9A9DA7;
                    }
                }
            }
        }
    }

    .td-form-group {
        .input-field {
            .input-group-text {
                background: transparent;
                border: 0;
                border-inline-end: 1px solid rgba($heading, $alpha: 0.16);
                margin-inline-start: 34px;

                @include dark-theme {
                    border-inline-end: 1px solid rgba($white, $alpha: 0.16);
                }
            }

            &.input-group {

                .form-control {
                    padding-inline-start: 16px;
                }
            }
        }
    }

    .input-group>:not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
        margin-inline-start: 0;
    }

    .select2-selection__rendered {
        margin-inline-start: 36px;
    }

    .select2-container--default {
        .select2-selection--single {
            height: 60px;
            line-height: 60px;
        }

        .select2-selection {
            border: 0;
        }
    }

    .select2-container--default .select2-selection {
        @include dark-theme {
            background-color: transparent;
            border-color: rgba($white, $alpha: 0.1);
        }
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        color: rgba(8, 8, 8, 0.65);

        @include dark-theme {
            color: #9A9DA7;
        }
    }

    .select2-container--default .select2-results__option {
        color: rgba(8, 8, 8, 0.65);

        @include dark-theme {
            color: #9A9DA7;
        }
    }

    .select2-dropdown {
        @include dark-theme {
            background-color: #341B81;
        }
    }

    .select2-container--default {
        .select2-search--dropdown {
            .select2-search__field {
                border-color: #9A9DA7;
                color: var(--td-heading);
            }
        }
    }
}

.auth-from-bottom-contents {
    .description {
        color: var(--td-text-primary);

        @include dark-theme {
            color: #9A9DA7;
        }
    }
}