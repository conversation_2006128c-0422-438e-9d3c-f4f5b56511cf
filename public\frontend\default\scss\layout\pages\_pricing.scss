@use '../../utils' as *;

/*----------------------------------------*/
/* Pricing styles
/*----------------------------------------*/
.td-pricing-section {
    @include dark-theme {
        background-color: var(--td-void);
    }

    .pricing-dot-bg {
        display: none;
        position: absolute;
        top: 0;
        left: 0;
        z-index: -1;
        width: 100%;
        height: 100%;
        background-repeat: no-repeat;

        @include dark-theme {
            display: block;
        }
    }
}

.pricing-tab-filter {
    &.td-tab {
        position: relative;
        display: inline-block;
        padding: 1px;

        .inner {
            display: inline-flex;
            position: relative;
            z-index: 3;
            background: var(--td-white);

            clip-path: polygon(5.116% 0.575%, 98.367% 0.575%, 98.367% 0.575%, 98.381% 0.579%, 98.395% 0.593%, 98.409% 0.614%, 98.423% 0.644%, 98.436% 0.682%, 98.449% 0.728%, 98.462% 0.782%, 98.474% 0.844%, 98.486% 0.913%, 98.497% 0.99%, 98.512% 1.112%, 99.873% 13.129%, 99.873% 13.129%, 99.885% 13.24%, 99.895% 13.358%, 99.905% 13.483%, 99.913% 13.614%, 99.92% 13.75%, 99.926% 13.891%, 99.931% 14.036%, 99.934% 14.184%, 99.936% 14.334%, 99.937% 14.487%, 99.937% 65.391%, 99.937% 65.391%, 99.936% 65.532%, 99.934% 65.671%, 99.931% 65.807%, 99.928% 65.941%, 99.923% 66.072%, 99.917% 66.199%, 99.91% 66.322%, 99.902% 66.44%, 99.893% 66.552%, 99.883% 66.659%, 99.867% 66.802%, 95.926% 98.823%, 95.926% 98.823%, 95.914% 98.912%, 95.902% 98.993%, 95.889% 99.065%, 95.875% 99.127%, 95.861% 99.181%, 95.847% 99.225%, 95.832% 99.26%, 95.817% 99.285%, 95.802% 99.3%, 95.787% 99.306%, 0.272% 99.306%, 0.272% 99.306%, 0.238% 99.281%, 0.206% 99.209%, 0.176% 99.094%, 0.149% 98.94%, 0.125% 98.751%, 0.104% 98.531%, 0.087% 98.283%, 0.074% 98.011%, 0.066% 97.72%, 0.063% 97.413%, 0.063% 41.89%, 0.063% 41.89%, 0.064% 41.749%, 0.066% 41.61%, 0.068% 41.474%, 0.072% 41.34%, 0.077% 41.209%, 0.083% 41.082%, 0.09% 40.959%, 0.098% 40.841%, 0.107% 40.728%, 0.117% 40.621%, 0.133% 40.479%, 4.976% 1.06%, 4.976% 1.06%, 4.988% 0.97%, 5.001% 0.89%, 5.014% 0.818%, 5.027% 0.755%, 5.041% 0.701%, 5.055% 0.656%, 5.07% 0.621%, 5.085% 0.596%, 5.1% 0.58%, 5.116% 0.575%);
            gap: 12px;
            padding: 20px 40px;

            @include dark-theme {
                background: var(--td-void);
            }

            @media #{$xs} {
                padding: 20px 30px;
                clip-path: none;
            }

            @media #{$xxs} {
                padding: 20px 20px;
            }

            &::before {
                position: absolute;
                top: 0;
                inset-inline-start: 0;
                content: "";
                background: var(--td-white);
                z-index: -1;
                width: 100%;
                height: 100%;

                @include dark-theme {
                    background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -36.7%, rgba(153, 153, 153, 0) 101.24%);
                }
            }
        }

        &::before {
            position: absolute;
            inset-inline-start: 0;
            top: 0;
            transition: all .3s;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, rgba(71, 118, 230, 1) 0%, rgba(142, 84, 233, 1) 100%);
            content: "";
            clip-path: polygon(5.116% 0.575%, 98.367% 0.575%, 98.367% 0.575%, 98.381% 0.579%, 98.395% 0.593%, 98.409% 0.614%, 98.423% 0.644%, 98.436% 0.682%, 98.449% 0.728%, 98.462% 0.782%, 98.474% 0.844%, 98.486% 0.913%, 98.497% 0.99%, 98.512% 1.112%, 99.873% 13.129%, 99.873% 13.129%, 99.885% 13.24%, 99.895% 13.358%, 99.905% 13.483%, 99.913% 13.614%, 99.92% 13.75%, 99.926% 13.891%, 99.931% 14.036%, 99.934% 14.184%, 99.936% 14.334%, 99.937% 14.487%, 99.937% 65.391%, 99.937% 65.391%, 99.936% 65.532%, 99.934% 65.671%, 99.931% 65.807%, 99.928% 65.941%, 99.923% 66.072%, 99.917% 66.199%, 99.91% 66.322%, 99.902% 66.44%, 99.893% 66.552%, 99.883% 66.659%, 99.867% 66.802%, 95.926% 98.823%, 95.926% 98.823%, 95.914% 98.912%, 95.902% 98.993%, 95.889% 99.065%, 95.875% 99.127%, 95.861% 99.181%, 95.847% 99.225%, 95.832% 99.26%, 95.817% 99.285%, 95.802% 99.3%, 95.787% 99.306%, 0.272% 99.306%, 0.272% 99.306%, 0.238% 99.281%, 0.206% 99.209%, 0.176% 99.094%, 0.149% 98.94%, 0.125% 98.751%, 0.104% 98.531%, 0.087% 98.283%, 0.074% 98.011%, 0.066% 97.72%, 0.063% 97.413%, 0.063% 41.89%, 0.063% 41.89%, 0.064% 41.749%, 0.066% 41.61%, 0.068% 41.474%, 0.072% 41.34%, 0.077% 41.209%, 0.083% 41.082%, 0.09% 40.959%, 0.098% 40.841%, 0.107% 40.728%, 0.117% 40.621%, 0.133% 40.479%, 4.976% 1.06%, 4.976% 1.06%, 4.988% 0.97%, 5.001% 0.89%, 5.014% 0.818%, 5.027% 0.755%, 5.041% 0.701%, 5.055% 0.656%, 5.07% 0.621%, 5.085% 0.596%, 5.1% 0.58%, 5.116% 0.575%);
            border-radius: 2px;

            @include dark-theme {
                background: linear-gradient(180deg, #091B52 0%, #0094FF 100%);
            }

            @media #{$xs} {
                clip-path: none;
            }
        }

        .nav-tabs {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;

            .nav-link {
                position: relative;
                display: inline-block;
                padding: 1px;
                background: transparent;

                .inner {
                    display: inline-flex;
                    position: relative;
                    z-index: 3;
                    clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
                    gap: 8px;
                    padding: 0 20px;
                    height: 40px;
                    align-items: center;
                    justify-content: center;
                    color: var(--td-heading);
                    font-weight: 600;

                    @media #{$xs} {
                        padding: 0 16px;
                    }

                    &::before {
                        position: absolute;
                        top: 0;
                        inset-inline-start: 0;
                        content: "";
                        background: linear-gradient(90deg, rgba(71, 118, 230, 0.1) 2.28%, rgba(142, 84, 233, 0.1) 100%);
                        z-index: -1;
                        width: 100%;
                        height: 100%;

                        @include dark-theme {
                            background: linear-gradient(90deg, rgba(71, 118, 230, 0.1) -8.58%, rgba(142, 84, 233, 0.1) 100%);
                        }
                    }

                    .btn-icon {
                        width: 20px;
                        display: inline-flex;
                    }
                }

                &::before {
                    position: absolute;
                    inset-inline-start: 0;
                    top: 0;
                    transition: all .3s;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(to right, #958CFF 0%, #958CFF 100%);
                    content: "";
                    clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
                    border-radius: 2px;
                }

                &.active {
                    .inner {
                        color: var(--td-white);

                        &:before {
                            background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
                        }
                    }

                    &:before {
                        background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
                    }

                    &:after {
                        display: none;
                    }
                }
            }
        }
    }
}

.pricing-tab-contents {
    max-width: 1920px;
}

.pricing-item {
    height: 100%;
    padding-top: 30px;

    .pricing-card {
        backdrop-filter: blur(10px);
        border-radius: 5px;
        position: relative;
        z-index: 1;
        height: 100%;

        .pricing-clip-path {
            position: relative;
            padding: 1px;
            height: 100%;
        }

        .pricing-card-inner {
            display: flex;
            position: relative;
            flex-direction: column;
            justify-content: space-between;
            gap: 30px;
            z-index: 3;
            background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -64.03%, rgba(9, 70, 255, 0.06) 86.17%);
            // background: #6D5EFF;
            clip-path: polygon(98.095% 0%, 1.905% 0%, 1.905% 0%, 1.596% 0.018%, 1.303% 0.071%, 1.029% 0.155%, 0.78% 0.268%, 0.558% 0.407%, 0.368% 0.569%, 0.213% 0.751%, 0.097% 0.95%, 0.025% 1.164%, 0% 1.389%, 0% 98.493%, 0% 98.493%, 0.025% 98.718%, 0.097% 98.932%, 0.213% 99.131%, 0.368% 99.313%, 0.558% 99.475%, 0.78% 99.614%, 1.029% 99.727%, 1.303% 99.811%, 1.596% 99.864%, 1.905% 99.882%, 86.903% 99.882%, 86.903% 99.882%, 87.041% 99.878%, 87.178% 99.867%, 87.312% 99.849%, 87.444% 99.825%, 87.573% 99.793%, 87.699% 99.755%, 87.821% 99.71%, 87.938% 99.659%, 88.051% 99.601%, 88.158% 99.538%, 99.35% 92.397%, 99.35% 92.397%, 99.468% 92.315%, 99.576% 92.226%, 99.672% 92.131%, 99.757% 92.031%, 99.83% 91.926%, 99.89% 91.817%, 99.938% 91.705%, 99.972% 91.589%, 99.993% 91.472%, 100% 91.352%, 100% 1.389%, 100% 1.389%, 99.975% 1.164%, 99.903% 0.95%, 99.787% 0.751%, 99.632% 0.569%, 99.442% 0.407%, 99.22% 0.268%, 98.971% 0.155%, 98.697% 0.071%, 98.404% 0.018%, 98.095% 0%);
            padding: 80px 40px 40px;
            height: 100%;

            @media #{$xs,$lg} {
                padding: 60px 20px 30px;
            }
        }

        .bg-shape {
            position: absolute;
            content: "";
            height: 100%;
            width: 100%;
            top: 0;
            inset-inline-start: 0;
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }

        .plan-icon {
            position: absolute;
            top: -30px;
            inset-inline-start: 30px;
            width: 100px;
            height: 100px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f0f4ff;
            backdrop-filter: blur(7.69231px);
            background-repeat: no-repeat;
            background-size: cover;
            z-index: 15;

            @include dark-theme {
                background: #040F30;
            }

            @media #{$xs} {
                width: 80px;
                height: 80px;
            }

            img {
                width: 50px;
                height: 50px;

                @media #{$xs} {
                    width: 36px;
                    height: 36px;
                }
            }
        }

        .plan-title {
            font-size: 30px;
            margin-top: 20px;
            margin-bottom: 5px;
            color: var(--td-heading);

            @media #{$lg,$md} {
                font-size: 26px;
            }

            @media #{$xs,$sm} {
                font-size: 24px;
            }
        }

        .plan-subtitle {
            font-style: italic;
            font-size: 14px;
            margin-top: 8px;

            @include dark-theme {
                color: rgba($white, $alpha: 0.8);
            }
        }

        .plan-price {
            font-size: 30px;
            font-weight: 700;
            margin-bottom: 20px;
            background: linear-gradient(90deg, #4776E6 56.79%, #8E54E9 97.46%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            display: inline-block;

            @include dark-theme {
                color: rgba($white, $alpha: 0.7);
            }

        }

        .price-period {
            font-size: 14px;
            font-weight: 400;
            color: #484848;

            @include dark-theme {
                color: var(--td-white);
            }
        }

        .feature-item {
            border-bottom: 2px solid #E0E3E9;
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            font-size: 14px;

            @include dark-theme {
                border-color: rgba($white, $alpha: 0.1);
            }
        }

        .feature-check {

            margin-inline-end: 8px;
            width: 16px;
            flex: 0 0 auto;

            img {
                @include rtl {
                    -webkit-transform: scaleX(-1);
                    transform: scaleX(-1);
                }
            }
        }

        .feature-text {
            span {
                font-weight: 600;

                @include dark-theme {
                    color: rgba($white, $alpha: 0.8);
                }
            }
        }

        .features-list {
            padding-top: 24px;
            border-top: 2px solid rgba($heading, $alpha: 0.1);

            @include dark-theme {
                border-color: rgba(255, 255, 255, 0.1);
            }

            ul {
                li {
                    list-style: none;
                    display: flex;

                    .feature-text {
                        @include dark-theme {
                            color: #9A9DA7;
                        }
                    }

                    &:not(:last-child) {
                        margin-bottom: 16px;
                    }
                }
            }
        }
    }

    .pricing-shapes {
        .shape-one {
            position: absolute;
            top: 0;
            inset-inline-end: 0;
            z-index: -1;
        }

        .shape-two {
            position: absolute;
            top: 0;
            inset-inline-start: 0;
            z-index: -1;
        }
    }

    .plan-featured-text {
        position: absolute;
        inset-inline-end: 14px;
        top: 14px;
        background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
        padding: 4px 14px;
        border-radius: 6px;
        color: var(--td-white);
        font-weight: 500;
        z-index: 6;
    }

}

.pricing-card {
    .pricing-card-inner {
        display: flex;
        position: relative;
        z-index: 3;
        background: #6D5EFF;
        clip-path: polygon(98.095% 0%, 1.905% 0%, 1.905% 0%, 1.596% 0.018%, 1.303% 0.071%, 1.029% 0.155%, 0.78% 0.268%, 0.558% 0.407%, 0.368% 0.569%, 0.213% 0.751%, 0.097% 0.95%, 0.025% 1.164%, 0% 1.389%, 0% 98.493%, 0% 98.493%, 0.025% 98.718%, 0.097% 98.932%, 0.213% 99.131%, 0.368% 99.313%, 0.558% 99.475%, 0.78% 99.614%, 1.029% 99.727%, 1.303% 99.811%, 1.596% 99.864%, 1.905% 99.882%, 86.903% 99.882%, 86.903% 99.882%, 87.041% 99.878%, 87.178% 99.867%, 87.312% 99.849%, 87.444% 99.825%, 87.573% 99.793%, 87.699% 99.755%, 87.821% 99.71%, 87.938% 99.659%, 88.051% 99.601%, 88.158% 99.538%, 99.35% 92.397%, 99.35% 92.397%, 99.468% 92.315%, 99.576% 92.226%, 99.672% 92.131%, 99.757% 92.031%, 99.83% 91.926%, 99.89% 91.817%, 99.938% 91.705%, 99.972% 91.589%, 99.993% 91.472%, 100% 91.352%, 100% 1.389%, 100% 1.389%, 99.975% 1.164%, 99.903% 0.95%, 99.787% 0.751%, 99.632% 0.569%, 99.442% 0.407%, 99.22% 0.268%, 98.971% 0.155%, 98.697% 0.071%, 98.404% 0.018%, 98.095% 0%);
        padding: 80px 40px 40px;
        height: 100%;
        flex-direction: column;
        justify-content: space-between;
    }
}

// price shape two
.td-pricing-two-section {
    background-color: var(--td-void);

}

.price-two-shapes {
    .shape-one {
        position: absolute;
        top: 0;
        inset-inline-start: 0;
        z-index: -1;
    }

    .shape-two {
        position: absolute;
        inset-inline-end: 0;
        bottom: 0;
    }
}

.pricing-overlay-bg {
    position: absolute;
    bottom: 0;
    inset-inline-start: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-position: bottom right;

    img {
        width: 100%;
    }
}

// Pricing style two
.pricing-tab-filter {
    &.td-tab {
        &.style-two {
            &::before {
                background: linear-gradient(90deg, rgba(71, 118, 230, 1) 0%, rgba(142, 84, 233, 1) 100%);

                @include dark-theme {
                    background: linear-gradient(180deg, #091B52 0%, #0094FF 100%);
                }
            }

            .inner {
                @include dark-theme {
                    background: var(--td-void);
                }

                &::before {
                    position: absolute;
                    top: 0;
                    inset-inline-start: 0;
                    content: "";
                    background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -36.7%, rgba(153, 153, 153, 0) 101.24%);
                    z-index: -1;
                    width: 100%;
                    height: 100%;
                }
            }

            .nav-tabs {
                .nav-link {
                    &.active {
                        &::before {
                            background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
                        }

                        .inner {
                            color: var(--td-white);

                            &:before {
                                background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
                            }
                        }
                    }

                    .inner {
                        color: var(--td-heading);
                    }
                }
            }
        }
    }
}

.pricing-item {
    &.style-two {
        .pricing-card {
            .pricing-card-inner {
                background: var(--td-white);

                @include dark-theme {
                    background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -64.03%, rgba(9, 70, 255, 0.06) 86.17%);
                }

            }

            .plan-icon {
                background-color: var(--td-white);

                @include dark-theme {
                    background-color: #040F30;
                }

                img {
                    width: 50px;
                    height: 50px;
                }
            }

            .plan-subtitle {
                color: var(--td-text-primary);
                font-size: 12px;
                margin-top: 8px;

                @include dark-theme {
                    color: #9A9DA7;
                }
            }

            .plan-price {
                color: #484848;

                @include dark-theme {
                    color: rgba($white, $alpha: 0.8);
                }
            }

            .features-list {
                ul {
                    li {
                        .feature-text {
                            @include dark-theme {
                                color: #9A9DA7;
                            }
                        }
                    }
                }
            }
        }
    }
}

.td-faq-section-two {
    @include dark-theme {
        background-color: var(--td-liberty-blue);
    }
}