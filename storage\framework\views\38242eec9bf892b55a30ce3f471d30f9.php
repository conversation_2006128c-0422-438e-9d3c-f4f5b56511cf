<?php $__env->startSection('theme-title'); ?>
    <?php echo e(__('Site Themes')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('theme-content'); ?>
    <?php $__currentLoopData = $themes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $theme): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="col-xl-4 col-lg-4 col-md-4 col-12">
            <div class="site-card">
                <div class="site-card-header">
                    <h3 class="title"><?php echo e(ucwords(str_replace('_', ' ', $theme->name))); ?> <?php echo e(__('Theme')); ?></h3>
                </div>
                <div class="site-card-body">
                    <div class="theme-img">
                        <?php if($theme->status): ?>
                            <div class="activated"><?php echo e(__('Activated')); ?></div>
                        <?php endif; ?>
                        <img class="w-100" src="<?php echo e(asset('backend/materials/theme/' . $theme->name . '.jpg')); ?>"
                            alt="">
                    </div>
                    <?php if($theme->status): ?>
                        <a href="#" class="site-btn w-100 centered mt-4 disabled"><i
                                data-lucide="circle-check"></i><?php echo e(__('Activated')); ?></a>
                    <?php else: ?>
                        <a href="<?php echo e(route('admin.theme.status-update', ['id' => $theme->id])); ?>"
                            class="site-btn black-btn w-100 centered mt-4"><i
                                data-lucide="check"></i><?php echo e(__('Active Now')); ?></a>
                    <?php endif; ?>

                </div>
            </div>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.theme.index', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/theme/site.blade.php ENDPATH**/ ?>