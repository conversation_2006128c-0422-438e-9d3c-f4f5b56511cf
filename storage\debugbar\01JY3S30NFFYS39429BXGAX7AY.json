{"__meta": {"id": "01JY3S30NFFYS39429BXGAX7AY", "datetime": "2025-06-19 15:42:29", "utime": **********.807842, "method": "GET", "uri": "/admin/page/section/why-work-with-us", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.298162, "end": **********.807854, "duration": 0.5096919536590576, "duration_str": "510ms", "measures": [{"label": "Booting", "start": **********.298162, "relative_start": 0, "end": **********.555895, "relative_end": **********.555895, "duration": 0.****************, "duration_str": "258ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.555909, "relative_start": 0.*****************, "end": **********.807856, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "252ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.570626, "relative_start": 0.****************, "end": **********.57417, "relative_end": **********.57417, "duration": 0.0035440921783447266, "duration_str": "3.54ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.604758, "relative_start": 0.***************, "end": **********.806292, "relative_end": **********.806292, "duration": 0.*****************, "duration_str": "202ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: backend.page.default.section.why-work-with-us", "start": **********.615227, "relative_start": 0.****************, "end": **********.615227, "relative_end": **********.615227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: backend.page.default.include.__language_bar", "start": **********.623462, "relative_start": 0.3252999782562256, "end": **********.623462, "relative_end": **********.623462, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: backend.page.default.section.include.__add_new_feature", "start": **********.637486, "relative_start": 0.3393239974975586, "end": **********.637486, "relative_end": **********.637486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: backend.page.default.section.include.__edit", "start": **********.645126, "relative_start": 0.34696412086486816, "end": **********.645126, "relative_end": **********.645126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: backend.page.default.section.include.__delete", "start": **********.651999, "relative_start": 0.3538370132446289, "end": **********.651999, "relative_end": **********.651999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: backend.layouts.app", "start": **********.659718, "relative_start": 0.3615560531616211, "end": **********.659718, "relative_end": **********.659718, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: backend.include.__head", "start": **********.666754, "relative_start": 0.3685920238494873, "end": **********.666754, "relative_end": **********.666754, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: global.admin_notify", "start": **********.684266, "relative_start": 0.38610410690307617, "end": **********.684266, "relative_end": **********.684266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: backend.include.__header", "start": **********.691722, "relative_start": 0.39355993270874023, "end": **********.691722, "relative_end": **********.691722, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: backend.include.__notification_data", "start": **********.746386, "relative_start": 0.4482240676879883, "end": **********.746386, "relative_end": **********.746386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: backend.include.__side_nav", "start": **********.757617, "relative_start": 0.4594550132751465, "end": **********.757617, "relative_end": **********.757617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: backend.include.__script", "start": **********.798295, "relative_start": 0.5001330375671387, "end": **********.798295, "relative_end": **********.798295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: global.__notification_script", "start": **********.80574, "relative_start": 0.5075781345367432, "end": **********.80574, "relative_end": **********.80574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 35227336, "peak_usage_str": "34MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.18.0", "PHP Version": "8.3.19", "Environment": "local", "Debug Mode": "Enabled", "URL": "orexcoin.test", "Timezone": "UTC", "Locale": "ar"}}, "views": {"count": 13, "nb_templates": 13, "templates": [{"name": "backend.page.default.section.why-work-with-us", "param_count": null, "params": [], "start": **********.615206, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/backend/page/default/section/why-work-with-us.blade.phpbackend.page.default.section.why-work-with-us", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Fpage%2Fdefault%2Fsection%2Fwhy-work-with-us.blade.php:1", "ajax": false, "filename": "why-work-with-us.blade.php", "line": "?"}}, {"name": "backend.page.default.include.__language_bar", "param_count": null, "params": [], "start": **********.623444, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/backend/page/default/include/__language_bar.blade.phpbackend.page.default.include.__language_bar", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Fpage%2Fdefault%2Finclude%2F__language_bar.blade.php:1", "ajax": false, "filename": "__language_bar.blade.php", "line": "?"}}, {"name": "backend.page.default.section.include.__add_new_feature", "param_count": null, "params": [], "start": **********.637467, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/backend/page/default/section/include/__add_new_feature.blade.phpbackend.page.default.section.include.__add_new_feature", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Fpage%2Fdefault%2Fsection%2Finclude%2F__add_new_feature.blade.php:1", "ajax": false, "filename": "__add_new_feature.blade.php", "line": "?"}}, {"name": "backend.page.default.section.include.__edit", "param_count": null, "params": [], "start": **********.64511, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/backend/page/default/section/include/__edit.blade.phpbackend.page.default.section.include.__edit", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Fpage%2Fdefault%2Fsection%2Finclude%2F__edit.blade.php:1", "ajax": false, "filename": "__edit.blade.php", "line": "?"}}, {"name": "backend.page.default.section.include.__delete", "param_count": null, "params": [], "start": **********.651986, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/backend/page/default/section/include/__delete.blade.phpbackend.page.default.section.include.__delete", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Fpage%2Fdefault%2Fsection%2Finclude%2F__delete.blade.php:1", "ajax": false, "filename": "__delete.blade.php", "line": "?"}}, {"name": "backend.layouts.app", "param_count": null, "params": [], "start": **********.659701, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/backend/layouts/app.blade.phpbackend.layouts.app", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Flayouts%2Fapp.blade.php:1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "backend.include.__head", "param_count": null, "params": [], "start": **********.666739, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__head.blade.phpbackend.include.__head", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__head.blade.php:1", "ajax": false, "filename": "__head.blade.php", "line": "?"}}, {"name": "global.admin_notify", "param_count": null, "params": [], "start": **********.68425, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/global/admin_notify.blade.phpglobal.admin_notify", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fglobal%2Fadmin_notify.blade.php:1", "ajax": false, "filename": "admin_notify.blade.php", "line": "?"}}, {"name": "backend.include.__header", "param_count": null, "params": [], "start": **********.691701, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.phpbackend.include.__header", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:1", "ajax": false, "filename": "__header.blade.php", "line": "?"}}, {"name": "backend.include.__notification_data", "param_count": null, "params": [], "start": **********.746371, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__notification_data.blade.phpbackend.include.__notification_data", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__notification_data.blade.php:1", "ajax": false, "filename": "__notification_data.blade.php", "line": "?"}}, {"name": "backend.include.__side_nav", "param_count": null, "params": [], "start": **********.757599, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__side_nav.blade.phpbackend.include.__side_nav", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__side_nav.blade.php:1", "ajax": false, "filename": "__side_nav.blade.php", "line": "?"}}, {"name": "backend.include.__script", "param_count": null, "params": [], "start": **********.798279, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__script.blade.phpbackend.include.__script", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__script.blade.php:1", "ajax": false, "filename": "__script.blade.php", "line": "?"}}, {"name": "global.__notification_script", "param_count": null, "params": [], "start": **********.805722, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\resources\\views/global/__notification_script.blade.phpglobal.__notification_script", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fglobal%2F__notification_script.blade.php:1", "ajax": false, "filename": "__notification_script.blade.php", "line": "?"}}]}, "queries": {"count": 19, "nb_statements": 19, "nb_visible_statements": 19, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.009040000000000001, "accumulated_duration_str": "9.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'RlVeXUqfaJFjRpxBhzt1tqTusMxu4a7ScBN9ievH' limit 1", "type": "query", "params": [], "bindings": ["RlVeXUqfaJFjRpxBhzt1tqTusMxu4a7ScBN9ievH"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.5803392, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "orexcoin", "explain": null, "start_percent": 0, "width_percent": 5.088}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.588856, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "orexcoin", "explain": null, "start_percent": 5.088, "width_percent": 6.527}, {"sql": "select * from `languages` where `is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\orexcoin\\app\\helpers.php", "line": 382}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.591934, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "helpers.php:382", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\orexcoin\\app\\helpers.php", "line": 382}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2Fhelpers.php:382", "ajax": false, "filename": "helpers.php", "line": "382"}, "connection": "orexcoin", "explain": null, "start_percent": 11.615, "width_percent": 3.208}, {"sql": "select * from `landing_pages` where `theme` = 'default' and `code` = 'why-work-with-us'", "type": "query", "params": [], "bindings": ["default", "why-work-with-us"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/PageController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Backend\\PageController.php", "line": 267}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.599825, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "PageController.php:267", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/PageController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Backend\\PageController.php", "line": 267}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FBackend%2FPageController.php:267", "ajax": false, "filename": "PageController.php", "line": "267"}, "connection": "orexcoin", "explain": null, "start_percent": 14.823, "width_percent": 4.314}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/PageController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Backend\\PageController.php", "line": 276}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.601145, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "PageController.php:276", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/PageController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Backend\\PageController.php", "line": 276}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FBackend%2FPageController.php:276", "ajax": false, "filename": "PageController.php", "line": "276"}, "connection": "orexcoin", "explain": null, "start_percent": 19.137, "width_percent": 2.655}, {"sql": "select * from `landing_contents` where `theme` = 'default' and `type` = 'why-work-with-us' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["default", "why-work-with-us", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/PageController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Backend\\PageController.php", "line": 284}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.602575, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "PageController.php:284", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/PageController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Backend\\PageController.php", "line": 284}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FBackend%2FPageController.php:284", "ajax": false, "filename": "PageController.php", "line": "284"}, "connection": "orexcoin", "explain": null, "start_percent": 21.792, "width_percent": 3.982}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Providers\\ViewServiceProvider.php", "line": 67}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 189}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160}], "start": **********.61385, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ViewServiceProvider.php:67", "source": {"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Providers\\ViewServiceProvider.php", "line": 67}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FProviders%2FViewServiceProvider.php:67", "ajax": false, "filename": "ViewServiceProvider.php", "line": "67"}, "connection": "orexcoin", "explain": null, "start_percent": 25.774, "width_percent": 5.088}, {"sql": "select * from `notifications` where `for` = 'admin' and `notifications`.`deleted_at` is null order by `created_at` desc limit 10", "type": "query", "params": [], "bindings": ["admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 2}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.6930928, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "backend.include.__header:2", "source": {"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 2}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:2", "ajax": false, "filename": "__header.blade.php", "line": "2"}, "connection": "orexcoin", "explain": null, "start_percent": 30.863, "width_percent": 12.168}, {"sql": "select * from `users` where `users`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 2}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.696724, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:2", "source": {"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 2}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:2", "ajax": false, "filename": "__header.blade.php", "line": "2"}, "connection": "orexcoin", "explain": null, "start_percent": 43.031, "width_percent": 5.42}, {"sql": "select count(*) as aggregate from `notifications` where `for` = 'admin' and `read` = 0 and `notifications`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["admin", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 3}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.697756, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:3", "source": {"index": 16, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 3}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:3", "ajax": false, "filename": "__header.blade.php", "line": "3"}, "connection": "orexcoin", "explain": null, "start_percent": 48.451, "width_percent": 3.429}, {"sql": "select * from `notifications` where `for` = 'admin' and `notifications`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.6985009, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:4", "source": {"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 4}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:4", "ajax": false, "filename": "__header.blade.php", "line": "4"}, "connection": "orexcoin", "explain": null, "start_percent": 51.881, "width_percent": 6.527}, {"sql": "select * from `users` where `users`.`id` in (1, 42, 43, 45)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 4}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.7007341, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:4", "source": {"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 4}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:4", "ajax": false, "filename": "__header.blade.php", "line": "4"}, "connection": "orexcoin", "explain": null, "start_percent": 58.407, "width_percent": 3.429}, {"sql": "select * from `notifications` where `for` = 'admin' and `notifications`.`deleted_at` is null order by `created_at` desc limit 10", "type": "query", "params": [], "bindings": ["admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.7017531, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:5", "source": {"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 5}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:5", "ajax": false, "filename": "__header.blade.php", "line": "5"}, "connection": "orexcoin", "explain": null, "start_percent": 61.836, "width_percent": 5.088}, {"sql": "select * from `users` where `users`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 5}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.702894, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:5", "source": {"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 5}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:5", "ajax": false, "filename": "__header.blade.php", "line": "5"}, "connection": "orexcoin", "explain": null, "start_percent": 66.925, "width_percent": 3.872}, {"sql": "select count(*) as aggregate from `notifications` where `for` = 'admin' and `read` = 0 and `notifications`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["admin", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 6}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.703846, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:6", "source": {"index": 16, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 6}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:6", "ajax": false, "filename": "__header.blade.php", "line": "6"}, "connection": "orexcoin", "explain": null, "start_percent": 70.796, "width_percent": 4.204}, {"sql": "select * from `notifications` where `for` = 'admin' and `notifications`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.70472, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:7", "source": {"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 7}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:7", "ajax": false, "filename": "__header.blade.php", "line": "7"}, "connection": "orexcoin", "explain": null, "start_percent": 75, "width_percent": 6.969}, {"sql": "select * from `users` where `users`.`id` in (1, 42, 43, 45)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 7}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}], "start": **********.707157, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:7", "source": {"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\orexcoin\\resources\\views/backend/include/__header.blade.php", "line": 7}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:7", "ajax": false, "filename": "__header.blade.php", "line": "7"}, "connection": "orexcoin", "explain": null, "start_percent": 81.969, "width_percent": 4.314}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (1) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\Admin'", "type": "query", "params": [], "bindings": ["App\\Models\\Admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.762694, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php:328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "orexcoin", "explain": null, "start_percent": 86.283, "width_percent": 8.518}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\Admin'", "type": "query", "params": [], "bindings": ["App\\Models\\Admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.764291, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php:241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "orexcoin", "explain": null, "start_percent": 94.801, "width_percent": 5.199}]}, "models": {"data": {"App\\Models\\Notification": {"value": 246, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FNotification.php:1", "ajax": false, "filename": "Notification.php", "line": "?"}}, "App\\Models\\User": {"value": 10, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Language": {"value": 4, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\LandingContent": {"value": 4, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FLandingContent.php:1", "ajax": false, "filename": "LandingContent.php", "line": "?"}}, "App\\Models\\LandingPage": {"value": 2, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FLandingPage.php:1", "ajax": false, "filename": "LandingPage.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FAdmin.php:1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php:1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 268, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 56, "messages": [{"message": "[\n  ability => customer-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-572839444 data-indent-pad=\"  \"><span class=sf-dump-note>customer-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">customer-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-572839444\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.767187, "xdebug_link": null}, {"message": "[\n  ability => customer-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-634580767 data-indent-pad=\"  \"><span class=sf-dump-note>customer-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">customer-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-634580767\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.76793, "xdebug_link": null}, {"message": "[\n  ability => customer-mail-send,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2080125395 data-indent-pad=\"  \"><span class=sf-dump-note>customer-mail-send </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">customer-mail-send</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2080125395\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.768572, "xdebug_link": null}, {"message": "[\n  ability => coin-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1270325214 data-indent-pad=\"  \"><span class=sf-dump-note>coin-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">coin-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1270325214\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.769231, "xdebug_link": null}, {"message": "[\n  ability => coin-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-202119008 data-indent-pad=\"  \"><span class=sf-dump-note>coin-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">coin-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-202119008\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.769628, "xdebug_link": null}, {"message": "[\n  ability => miner-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-377746553 data-indent-pad=\"  \"><span class=sf-dump-note>miner-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">miner-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-377746553\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.770351, "xdebug_link": null}, {"message": "[\n  ability => schedule-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1236723614 data-indent-pad=\"  \"><span class=sf-dump-note>schedule-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">schedule-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1236723614\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.770903, "xdebug_link": null}, {"message": "[\n  ability => schema-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-235313709 data-indent-pad=\"  \"><span class=sf-dump-note>schema-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">schema-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-235313709\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.771403, "xdebug_link": null}, {"message": "[\n  ability => holiday_edit,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-401679398 data-indent-pad=\"  \"><span class=sf-dump-note>holiday_edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">holiday_edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-401679398\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.771871, "xdebug_link": null}, {"message": "[\n  ability => schema-edit,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-831073189 data-indent-pad=\"  \"><span class=sf-dump-note>schema-edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">schema-edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-831073189\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.772305, "xdebug_link": null}, {"message": "[\n  ability => kyc-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-326426332 data-indent-pad=\"  \"><span class=sf-dump-note>kyc-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">kyc-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-326426332\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.772585, "xdebug_link": null}, {"message": "[\n  ability => kyc-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-867827749 data-indent-pad=\"  \"><span class=sf-dump-note>kyc-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">kyc-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-867827749\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.772961, "xdebug_link": null}, {"message": "[\n  ability => kyc-form-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-715093127 data-indent-pad=\"  \"><span class=sf-dump-note>kyc-form-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">kyc-form-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-715093127\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.773451, "xdebug_link": null}, {"message": "[\n  ability => role-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1128764193 data-indent-pad=\"  \"><span class=sf-dump-note>role-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">role-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1128764193\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.773781, "xdebug_link": null}, {"message": "[\n  ability => role-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1772672666 data-indent-pad=\"  \"><span class=sf-dump-note>role-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">role-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1772672666\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.774156, "xdebug_link": null}, {"message": "[\n  ability => staff-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-198879455 data-indent-pad=\"  \"><span class=sf-dump-note>staff-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">staff-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-198879455\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.774485, "xdebug_link": null}, {"message": "[\n  ability => automatic-gateway-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1298278141 data-indent-pad=\"  \"><span class=sf-dump-note>automatic-gateway-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">automatic-gateway-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1298278141\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.774813, "xdebug_link": null}, {"message": "[\n  ability => transaction-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1040482026 data-indent-pad=\"  \"><span class=sf-dump-note>transaction-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">transaction-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1040482026\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.775117, "xdebug_link": null}, {"message": "[\n  ability => user-minings-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-786765949 data-indent-pad=\"  \"><span class=sf-dump-note>user-minings-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">user-minings-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-786765949\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.775516, "xdebug_link": null}, {"message": "[\n  ability => automatic-gateway-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-702034886 data-indent-pad=\"  \"><span class=sf-dump-note>automatic-gateway-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">automatic-gateway-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-702034886\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.775853, "xdebug_link": null}, {"message": "[\n  ability => automatic-gateway-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1892581713 data-indent-pad=\"  \"><span class=sf-dump-note>automatic-gateway-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">automatic-gateway-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1892581713\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.776092, "xdebug_link": null}, {"message": "[\n  ability => automatic-gateway-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1585980460 data-indent-pad=\"  \"><span class=sf-dump-note>automatic-gateway-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">automatic-gateway-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1585980460\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.776481, "xdebug_link": null}, {"message": "[\n  ability => manual-gateway-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1710629760 data-indent-pad=\"  \"><span class=sf-dump-note>manual-gateway-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manual-gateway-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1710629760\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.776858, "xdebug_link": null}, {"message": "[\n  ability => deposit-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1622805285 data-indent-pad=\"  \"><span class=sf-dump-note>deposit-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">deposit-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1622805285\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.777215, "xdebug_link": null}, {"message": "[\n  ability => withdraw-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1993330569 data-indent-pad=\"  \"><span class=sf-dump-note>withdraw-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">withdraw-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1993330569\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.777638, "xdebug_link": null}, {"message": "[\n  ability => withdraw-method-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1449723629 data-indent-pad=\"  \"><span class=sf-dump-note>withdraw-method-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">withdraw-method-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1449723629\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.777958, "xdebug_link": null}, {"message": "[\n  ability => withdraw-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1566953394 data-indent-pad=\"  \"><span class=sf-dump-note>withdraw-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">withdraw-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1566953394\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.77831, "xdebug_link": null}, {"message": "[\n  ability => withdraw-schedule,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-138808718 data-indent-pad=\"  \"><span class=sf-dump-note>withdraw-schedule </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">withdraw-schedule</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-138808718\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.778698, "xdebug_link": null}, {"message": "[\n  ability => withdraw-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1718233319 data-indent-pad=\"  \"><span class=sf-dump-note>withdraw-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">withdraw-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1718233319\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.779037, "xdebug_link": null}, {"message": "[\n  ability => referral-create,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1745842890 data-indent-pad=\"  \"><span class=sf-dump-note>referral-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">referral-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1745842890\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.779446, "xdebug_link": null}, {"message": "[\n  ability => manage-portfolio,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1249205901 data-indent-pad=\"  \"><span class=sf-dump-note>manage-portfolio </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage-portfolio</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1249205901\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.779886, "xdebug_link": null}, {"message": "[\n  ability => site-setting,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1029341310 data-indent-pad=\"  \"><span class=sf-dump-note>site-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">site-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1029341310\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.780324, "xdebug_link": null}, {"message": "[\n  ability => site-setting,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1254541696 data-indent-pad=\"  \"><span class=sf-dump-note>site-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">site-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1254541696\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.780607, "xdebug_link": null}, {"message": "[\n  ability => site-setting,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1821316880 data-indent-pad=\"  \"><span class=sf-dump-note>site-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">site-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1821316880\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.780975, "xdebug_link": null}, {"message": "[\n  ability => email-setting,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2063419342 data-indent-pad=\"  \"><span class=sf-dump-note>email-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">email-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2063419342\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.78135, "xdebug_link": null}, {"message": "[\n  ability => site-setting,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1728240917 data-indent-pad=\"  \"><span class=sf-dump-note>site-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">site-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1728240917\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.781716, "xdebug_link": null}, {"message": "[\n  ability => language-setting,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-190372592 data-indent-pad=\"  \"><span class=sf-dump-note>language-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">language-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-190372592\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.782088, "xdebug_link": null}, {"message": "[\n  ability => page-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1993803370 data-indent-pad=\"  \"><span class=sf-dump-note>page-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">page-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1993803370\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.782478, "xdebug_link": null}, {"message": "[\n  ability => plugin-setting,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1821209352 data-indent-pad=\"  \"><span class=sf-dump-note>plugin-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">plugin-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1821209352\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.782849, "xdebug_link": null}, {"message": "[\n  ability => notification-tune-setting,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-604532596 data-indent-pad=\"  \"><span class=sf-dump-note>notification-tune-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">notification-tune-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-604532596\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.783222, "xdebug_link": null}, {"message": "[\n  ability => landing-page-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1188715381 data-indent-pad=\"  \"><span class=sf-dump-note>landing-page-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">landing-page-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1188715381\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.783605, "xdebug_link": null}, {"message": "[\n  ability => landing-page-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-614542357 data-indent-pad=\"  \"><span class=sf-dump-note>landing-page-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">landing-page-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-614542357\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.78387, "xdebug_link": null}, {"message": "[\n  ability => custom-css,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1323446995 data-indent-pad=\"  \"><span class=sf-dump-note>custom-css </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">custom-css</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1323446995\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.784389, "xdebug_link": null}, {"message": "[\n  ability => page-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-666274797 data-indent-pad=\"  \"><span class=sf-dump-note>page-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">page-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-666274797\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.784836, "xdebug_link": null}, {"message": "[\n  ability => footer-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1021744933 data-indent-pad=\"  \"><span class=sf-dump-note>footer-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">footer-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1021744933\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.785737, "xdebug_link": null}, {"message": "[\n  ability => page-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-103656459 data-indent-pad=\"  \"><span class=sf-dump-note>page-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">page-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-103656459\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.78627, "xdebug_link": null}, {"message": "[\n  ability => navigation-manage,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1387801974 data-indent-pad=\"  \"><span class=sf-dump-note>navigation-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">navigation-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1387801974\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.787208, "xdebug_link": null}, {"message": "[\n  ability => subscriber-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2053967451 data-indent-pad=\"  \"><span class=sf-dump-note>subscriber-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">subscriber-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2053967451\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.787629, "xdebug_link": null}, {"message": "[\n  ability => template-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-954684816 data-indent-pad=\"  \"><span class=sf-dump-note>template-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">template-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-954684816\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.788007, "xdebug_link": null}, {"message": "[\n  ability => subscriber-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-14328402 data-indent-pad=\"  \"><span class=sf-dump-note>subscriber-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">subscriber-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-14328402\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.788383, "xdebug_link": null}, {"message": "[\n  ability => subscriber-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2030801873 data-indent-pad=\"  \"><span class=sf-dump-note>subscriber-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">subscriber-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2030801873\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.78874, "xdebug_link": null}, {"message": "[\n  ability => support-ticket-list,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-964780376 data-indent-pad=\"  \"><span class=sf-dump-note>support-ticket-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">support-ticket-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-964780376\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.789121, "xdebug_link": null}, {"message": "[\n  ability => manage-cron-job,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1631648690 data-indent-pad=\"  \"><span class=sf-dump-note>manage-cron-job </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage-cron-job</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1631648690\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.789537, "xdebug_link": null}, {"message": "[\n  ability => manage-cron-job,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-486984568 data-indent-pad=\"  \"><span class=sf-dump-note>manage-cron-job </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage-cron-job</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-486984568\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.789921, "xdebug_link": null}, {"message": "[\n  ability => clear-cache,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-987439161 data-indent-pad=\"  \"><span class=sf-dump-note>clear-cache </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">clear-cache</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-987439161\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.790312, "xdebug_link": null}, {"message": "[\n  ability => application-details,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-752078929 data-indent-pad=\"  \"><span class=sf-dump-note>application-details </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">application-details</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-752078929\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.790702, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://orexcoin.test/admin/page/section/why-work-with-us", "action_name": "admin.page.section.section", "controller_action": "App\\Http\\Controllers\\Backend\\PageController@landingSection", "uri": "GET admin/page/section/{section}", "controller": "App\\Http\\Controllers\\Backend\\PageController@landingSection<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FBackend%2FPageController.php:264\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/page", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FBackend%2FPageController.php:264\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/PageController.php:264-287</a>", "middleware": "web, auth:admin", "duration": "511ms", "peak_memory": "36MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-8365464 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-8365464\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-943947134 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-943947134\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1296438380 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"735 characters\">declinecookies=true; XSRF-TOKEN=eyJpdiI6IlJtSElSMzNaMTVrYkNZdlZCOVM0cVE9PSIsInZhbHVlIjoiU2pqNEVlRmNzNE50c2l4R3lISy9lUVVHMnBRcmpyaUtqTFVvWnRSVmRlZDVXWlZyZDdUb24vd1ZuSytndExMRmltRFVYRWVrSWlGTnhVRnJtT3AyYUtvMndCaTB5WmpZajVKamtOUHIyOUlEK05QUGVwb3dDblR0SUxlcDdQMFQiLCJtYWMiOiI3ZjQ5MjZkMDY2NWUzNWUwYWQxMzIzNmNkYjJiNzY5YjE3YzNmMmE3ZjMyZDdiNjZmMjg2ODMyZDRmYjU4ZjJjIiwidGFnIjoiIn0%3D; orexcoin_session=eyJpdiI6IjlWNVE3eHhRdmVUeURzQmVOZmJyR3c9PSIsInZhbHVlIjoiVGlqR1JVOEp2K2luK1NCenZPQjFQOUx4WTE1WXhJZ2lqUGptdUJ2LzZ4RTJTUkxXMW1hS2VGRDkwbFNqK211QTcwdkwvUjlnZTZPSzZLNVJQQkxDeFlndHVnZSt2djBLTmIvUHdTZGx4MU5FTEYwOUVtbzZLTGIvOHdwZEh6YWEiLCJtYWMiOiJhNDIyYjYxOTZlNGM2ZGZhZjE1ZWYxYjFkZjdiNmNmZDZmNzYwMTM4ZWRmNDM3NDgyODViOTJhODA4YTcxZGRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">https://orexcoin.test/admin/page/section/why-work-with-us</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">orexcoin.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1296438380\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-930686687 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>declinecookies</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GX2dwKYOxZeuJn88ful1fEGUvgF3cILWy6yznC8F</span>\"\n  \"<span class=sf-dump-key>orexcoin_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RlVeXUqfaJFjRpxBhzt1tqTusMxu4a7ScBN9ievH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-930686687\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-660989012 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 19 Jun 2025 09:42:29 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-660989012\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1515429334 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GX2dwKYOxZeuJn88ful1fEGUvgF3cILWy6yznC8F</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">https://orexcoin.test/favicon.ico</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>theme_mode</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>45</span>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1515429334\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://orexcoin.test/admin/page/section/why-work-with-us", "action_name": "admin.page.section.section", "controller_action": "App\\Http\\Controllers\\Backend\\PageController@landingSection"}, "badge": null}}