// ==================================================
// * Project Name   : OreXcoin – Modern Cryptocurrency Mining & Earnings System
// * File           :  JS Base
// * Version        :  1.0
// * Last change    :  04 june 2025, Sunday
// * Author         :  tdevs (https://codecanyon.net/user/tdevs/portfolio)
// ==================================================

(function ($) {
  'use strict';

  // Mobile dropdown menu
  $(document).ready(function () {
    $(".mobile-menu ul ul").hide();

    // Main Menu Click
    $(".mobile-menu > ul > li > a").click(function (e) {

      const $submenu = $(this).siblings("ul");
      const $icon = $(this).find("i");

      if ($submenu.length > 0) {
        const isOpen = $submenu.is(":visible");

        // Close other main menus
        $(".mobile-menu > ul > li > ul").not($submenu).slideUp(300);
        $(".mobile-menu > ul > li > a i").removeClass("ic--round-minus").addClass("ic--round-plus");

        // Toggle the clicked menu
        if (!isOpen) {
          $submenu.stop(true, true).slideDown(300);
          $icon.removeClass("ic--round-plus").addClass("ic--round-minus");
        } else {
          $submenu.stop(true, true).slideUp(300);
          $icon.removeClass("ic--round-minus").addClass("ic--round-plus");
        }
      }
    });

    // Submenu Click (Nested Menus)
    $(".mobile-menu ul ul li > a").click(function (e) {
      e.preventDefault();

      const $submenu = $(this).siblings("ul");
      const $icon = $(this).find("i");

      if ($submenu.length > 0) {
        const isOpen = $submenu.is(":visible");

        // Close only sibling submenus
        $(this).parent().siblings().find("ul").slideUp(300);
        $(this).parent().siblings().find("i").removeClass("ic--round-minus").addClass("ic--round-plus");

        // Toggle clicked submenu
        if (!isOpen) {
          $submenu.stop(true, true).slideDown(300);
          $icon.removeClass("ic--round-plus").addClass("ic--round-minus");
        } else {
          $submenu.stop(true, true).slideUp(300);
          $icon.removeClass("ic--round-minus").addClass("ic--round-plus");
        }
      }
    });
  });

  // Offcanvas Js
  $(".offcanvas-close,.offcanvas-overlay").on("click", function () {
    $(".offcanvas-area").removeClass("info-open");
    $(".offcanvas-overlay").removeClass("overlay-open");
  });
  $(".sidebar-toggle").on("click", function () {
    $(".offcanvas-area").addClass("info-open");
    $(".offcanvas-overlay").addClass("overlay-open");
  });

  // Body overlay Js
  $(".body-overlay").on("click", function () {
    $(".offcanvas-area").removeClass("opened");
    $(".body-overlay").removeClass("opened");
  });

  // Header sticky
  $(window).scroll(function () {
    if ($(this).scrollTop() > 250) {
      $("#header-sticky").addClass("active-sticky");
    } else {
      $("#header-sticky").removeClass("active-sticky");
    }
  });

  // Back to top js  
  if ($(".back-to-top-wrap path").length > 0) {
    var progressPath = document.querySelector(".back-to-top-wrap path");
    var pathLength = progressPath.getTotalLength();
    progressPath.style.transition = progressPath.style.WebkitTransition =
      "none";
    progressPath.style.strokeDasharray = pathLength + " " + pathLength;
    progressPath.style.strokeDashoffset = pathLength;
    progressPath.getBoundingClientRect();
    progressPath.style.transition = progressPath.style.WebkitTransition =
      "stroke-dashoffset 10ms linear";
    var updateProgress = function () {
      var scroll = $(window).scrollTop();
      var height = $(document).height() - $(window).height();
      var progress = pathLength - (scroll * pathLength) / height;
      progressPath.style.strokeDashoffset = progress;
    };
    updateProgress();
    $(window).scroll(updateProgress);
    var offset = 150;
    var duration = 550;
    jQuery(window).on("scroll", function () {
      if (jQuery(this).scrollTop() > offset) {
        jQuery(".back-to-top-wrap").addClass("active-progress");
      } else {
        jQuery(".back-to-top-wrap").removeClass("active-progress");
      }
    });
    jQuery(".back-to-top-wrap").on("click", function (event) {
      event.preventDefault();
      jQuery("html, body").animate({
        scrollTop: 0
      }, duration);
      return false;
    });
  }

  // Apply background image from 'data-background' attribute
  $("[data-background]").each(function () {
    let bg = $(this).attr("data-background");
    if (bg) {
      $(this).css("background-image", `url(${bg})`);
    }
  });

  // Set width from 'data-width' attribute
  $("[data-width]").each(function () {
    let width = $(this).attr("data-width");
    if (width) {
      $(this).css("width", width);
    }
  });

  // Apply background color from 'data-bg-color' attribute
  $("[data-bg-color]").each(function () {
    let bgColor = $(this).attr("data-bg-color");
    if (bgColor) {
      $(this).css("background-color", bgColor);
    }
  });

  // Initialize Select2
  $(function () {
    if ($.fn.select2) {
      // Function to render icons with images
      function renderIcon(option) {
        if (!option.id) return option.text;

        const imgSrc = $(option.element).data("image");
        const iconClass = $(option.element).data("icon");

        const imgHtml = imgSrc ? `<img src="${imgSrc}" style="width:20px; margin-right:5px;">` : "";
        const iconHtml = iconClass ? `<i class="${iconClass} mr-1"></i>` : "";

        return $(`<span>${imgHtml} ${iconHtml} ${option.text}</span>`);
      }

      // Initialize Select2 with Icons & Images
      $('.select2Icons').each(function () {
        $(this).select2({
          dropdownParent: $(this).parent(),
          templateResult: renderIcon,
          templateSelection: renderIcon,
          escapeMarkup: function (markup) {
            return markup;
          }
        });
      });

      // Function to render flags (for `.defaultselect2`)
      function renderFlag(option) {
        if (!option.id) return option.text;

        const flagClass = $(option.element).data("icon");
        const flagHtml = flagClass ? `<i class="${flagClass} mr-1"></i>` : "";

        return $(`<span>${flagHtml} ${option.text}</span>`);
      }

      // Initialize Select2 with Flags (for `.defaultselect2`)
      $('.defaultselect2').each(function () {
        $(this).select2({
          dropdownParent: $(this).parent(),
          templateResult: renderFlag,
          templateSelection: function (option) {
            if ($(this).attr("multiple")) {
              return option.text;
            }
            return renderFlag(option);
          },
          minimumResultsForSearch: -1,
          escapeMarkup: function (markup) {
            return markup;
          }
        });
      });
    }
  });

  // Bootstrap tooltip activation
  var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
  var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl)
  })
  tooltipTriggerList.map(function (tooltipTriggerEl) {
    new bootstrap.Tooltip(tooltipTriggerEl, {
      customClass: 'custom-tooltip'
    });
  });

  // Initialize dropdowns
  document.addEventListener('DOMContentLoaded', () => {
    // Notification Dropdown Logic
    function setupNotificationDropdown() {
      const notificationBox = document.querySelector('.notification-panel-box');
      const notificationPanel = document.querySelector('.notification-panel');
      const closeButtons = document.querySelectorAll('.notification-item .close-btn');
      const dropdownClose = document.querySelector('.notifications-dropdown-close');

      if (notificationBox && notificationPanel) {
        notificationBox.addEventListener('click', (e) => {
          e.stopPropagation();
          closeAllDropdowns();
          notificationPanel.classList.toggle('active');
        });

        if (dropdownClose) {
          dropdownClose.addEventListener('click', (e) => {
            e.stopPropagation();
            notificationPanel.classList.remove('active');
          });
        }

        closeButtons.forEach((btn) => {
          btn.addEventListener('click', (e) => {
            e.target.closest('.notification-item').remove();
          });
        });

        notificationPanel.addEventListener('click', (e) => {
          e.stopPropagation();
        });
        document.addEventListener('click', () => {
          notificationPanel.classList.remove('active');
        });
      }
    }

    // User Profile Dropdown Logic
    function setupUserProfileDropdown() {
      const userProfileDrop = document.querySelector('.user-profile-drop');
      const profileDropdownMenu = document.querySelector('.dropdown-menu');

      if (userProfileDrop && profileDropdownMenu) {
        userProfileDrop.addEventListener('click', (event) => {
          event.stopPropagation();
          closeAllDropdowns();
          profileDropdownMenu.classList.toggle('show');
        });

        // Close profile dropdown if clicking outside
        document.addEventListener('click', () => {
          profileDropdownMenu.classList.remove('show');
        });
      }
    }

    // Language Dropdown Logic
    function setupLanguageDropdown() {
      $(document).on("click", function (e) {
        const target = $(e.target);

        if (!target.closest(".translate_wrapper").length) {
          closeAllDropdowns(); // Close all other dropdowns
        }
      });

      $(document).on("click", function (e) {
        $(".translate_wrapper, .more_lang").removeClass("active");
      });
      $(".translate_wrapper .current_lang").on("click", function (e) {
        e.stopPropagation();
        const parent = $(this).parent();

        // Toggle the active class
        if (!parent.hasClass("active")) {
          closeAllDropdowns();
        }
        parent.toggleClass("active");

        setTimeout(() => {
          $(".more_lang").toggleClass("active");
        }, 5);
      });
    }

    // Function to close all dropdowns
    function closeAllDropdowns() {
      // Close notification panel and other dropdowns
      const openDropdowns = document.querySelectorAll('.dropdown-menu.show, .notification-panel.active');
      openDropdowns.forEach(dropdown => dropdown.classList.remove('show', 'active'));

      // Close the language dropdown
      $(".translate_wrapper, .more_lang").removeClass("active");
    }

    // Initialize all dropdowns
    setupNotificationDropdown();
    setupUserProfileDropdown();
    setupLanguageDropdown();
  });

  // Initialize language switcher
  var tnum = "en";
  $(document).ready(function () {

    translate(tnum);

    // Event listener for language selection
    $(".more_lang .lang").on("click", function () {
      $(this).addClass("selected").siblings().removeClass("selected");

      // Close the language dropdown
      $(".more_lang").removeClass("active");
      var i = $(this).find("i").attr("class");
      var lang = $(this).attr("data-value");

      // Update the global language variable
      var tnum = lang;
      translate(tnum);

      // Update the current language display
      $(".current_lang .lang-txt").text(lang);
      $(".current_lang i").attr("class", i);
    });
  });

  // Array of translations
  var trans = [
    {
      en: "General",
      pt: "Geral",
      es: "Generalo",
      fr: "GÃƒÂ©nÃƒÂ©rale",
      de: "Generel",
      cn: "Ã¤Â¸â‚¬Ã¨Ë†Â¬",
      ae: "Ã˜Â­Ã˜Â¬Ã™â€ Ã˜Â±Ã˜Â§Ã™â€ž Ã™â€žÃ™Ë†Ã˜Â§Ã˜Â¡",
    },
    {
      en: "Dashboards,widgets & layout.",
      pt: "PainÃƒÂ©is, widgets e layout.",
      es: "Paneloj, fenestraÃ„Âµoj kaj aranÃ„Âo.",
      fr: "Tableaux de bord, widgets et mise en page.",
      de: "Dashboards, widgets en lay-out.",
      cn: "Ã¤Â»ÂªÃ¨Â¡Â¨Ã¦ÂÂ¿Ã¯Â¼Å’Ã¥Â°ÂÃ¥Â·Â¥Ã¥â€¦Â·Ã¥â€™Å’Ã¥Â¸Æ’Ã¥Â±â‚¬Ã£â‚¬â€š",
      ae: "Ã™â€žÃ™Ë†Ã˜Â­Ã˜Â§Ã˜Âª Ã˜Â§Ã™â€žÃ™â€¦Ã˜Â¹Ã™â€žÃ™Ë†Ã™â€¦Ã˜Â§Ã˜Âª Ã™Ë†Ã˜Â§Ã™â€žÃ˜Â£Ã˜Â¯Ã™Ë†Ã˜Â§Ã˜Âª Ã™Ë†Ã˜Â§Ã™â€žÃ˜ÂªÃ˜Â®Ã˜Â·Ã™Å Ã˜Â·.",
    },
    {
      en: "Dashboards",
      pt: "PainÃƒÂ©is",
      es: "Paneloj",
      fr: "Tableaux",
      de: "Dashboards",
      cn: " Ã¤Â»ÂªÃ¨Â¡Â¨Ã¦ÂÂ¿ ",
      ae: "Ã™Ë†Ã˜Â­Ã˜Â§Ã˜Âª Ã˜Â§Ã™â€žÃ™â€šÃ™Å Ã˜Â§Ã˜Â¯Ã˜Â© ",
    },
    {
      en: "Default",
      pt: "PadrÃƒÂ£o",
      es: "Vaikimisi",
      fr: "DÃƒÂ©faut",
      de: "Standaard",
      cn: "Ã©â€ºÂ»Ã¥Â­ÂÃ¥â€¢â€ Ã¥â€¹â„¢",
      ae: "Ã™Ë†Ã˜Â¥Ã™ÂÃ˜ÂªÃ˜Â±Ã˜Â§Ã˜Â¶Ã™Å ",
    },
    {
      en: "Ecommerce",
      pt: "ComÃƒÂ©rcio eletrÃƒÂ´nico",
      es: "Komerco",
      fr: "Commerce ÃƒÂ©lectronique",
      de: "E-commerce",
      cn: "Ã©â€ºÂ»Ã¥Â­ÂÃ¥â€¢â€ Ã¥â€¹â„¢",
      ae: "Ã™Ë†Ã˜Â§Ã™â€žÃ˜ÂªÃ˜Â¬Ã˜Â§Ã˜Â±Ã˜Â© Ã˜Â§Ã™â€žÃ˜Â¥Ã™â€žÃ™Æ’Ã˜ÂªÃ˜Â±Ã™Ë†Ã™â€ Ã™Å Ã˜Â©",
    },
    {
      en: "Widgets",
      pt: "Ferramenta",
      es: "Vidin",
      fr: "Widgets",
      de: "Widgets",
      cn: "Ã¥Â°ÂÃ©Æ’Â¨Ã¤Â»Â¶",
      ae: "Ã™Ë†Ã˜Â§Ã™â€žÃ˜Â­Ã˜Â§Ã˜Â¬Ã™Å Ã˜Â§Ã˜Âª",
    },
    {
      en: "Page layout",
      pt: "Layout da pÃƒÂ¡gina",
      es: "PaÃ„Âa aranÃ„Âo",
      fr: "Tableaux",
      de: "Mise en page",
      cn: "Ã© ÂÃ©ÂÂ¢Ã¤Â½Ë†Ã¥Â±â‚¬",
      ae: "Ã™Ë†Ã˜ÂªÃ˜Â®Ã˜Â·Ã™Å Ã˜Â· Ã˜Â§Ã™â€žÃ˜ÂµÃ™ÂÃ˜Â­Ã˜Â©",
    },
    {
      en: "Applications",
      pt: "FormulÃƒÂ¡rios",
      es: "Aplikoj",
      fr: "Applications",
      de: "Toepassingen",
      cn: "Ã¦â€¡â€°Ã§â€Â¨Ã© ËœÃ¥Å¸Å¸",
      ae: "Ã™Ë†Ã˜Â§Ã™â€žÃ˜ÂªÃ˜Â·Ã˜Â¨Ã™Å Ã™â€šÃ˜Â§Ã˜Âª",
    },
    {
      en: "Ready to use Apps",
      pt: "Pronto para usar aplicativos",
      es: "Preta uzi Apps",
      fr: " Applications prÃƒÂªtes Ãƒ  lemploi ",
      de: "Klaar om apps te gebruiken",
      cn: "Ã¤Â»ÂªÃ¨Â¡Â¨Ã¦ÂÂ¿",
      ae: "Ã˜Â¬Ã˜Â§Ã™â€žÃ˜Â² Ã™â€žÃ˜Â§Ã˜Â³Ã˜ÂªÃ˜Â®Ã˜Â¯Ã˜Â§Ã™â€¦ Ã˜Â§Ã™â€žÃ˜ÂªÃ˜Â·Ã˜Â¨Ã™Å Ã™â€šÃ˜Â§Ã˜Âª",
    },
  ];

  // Function to handle translation
  function translate(tnum) {
    for (var i = 1; i < 9; i++) {
      $(".lan-" + i).text(trans[i - 1][tnum]);
    }
  }

  var trans = [
    {
      en: "General",
      pt: "Geral",
      es: "Generalo",
      fr: "GÃƒÂ©nÃƒÂ©rale",
      de: "Generel",
      cn: "Ã¤Â¸â‚¬Ã¨Ë†Â¬",
      ae: "Ã˜Â­Ã˜Â¬Ã™â€ Ã˜Â±Ã˜Â§Ã™â€ž Ã™â€žÃ™Ë†Ã˜Â§Ã˜Â¡",
    },
    {
      en: "Dashboards,widgets & layout.",
      pt: "PainÃƒÂ©is, widgets e layout.",
      es: "Paneloj, fenestraÃ„Âµoj kaj aranÃ„Âo.",
      fr: "Tableaux de bord, widgets et mise en page.",
      de: "Dashboards, widgets en lay-out.",
      cn: "Ã¤Â»ÂªÃ¨Â¡Â¨Ã¦ÂÂ¿Ã¯Â¼Å’Ã¥Â°ÂÃ¥Â·Â¥Ã¥â€¦Â·Ã¥â€™Å’Ã¥Â¸Æ’Ã¥Â±â‚¬Ã£â‚¬â€š",
      ae: "Ã™â€žÃ™Ë†Ã˜Â­Ã˜Â§Ã˜Âª Ã˜Â§Ã™â€žÃ™â€¦Ã˜Â¹Ã™â€žÃ™Ë†Ã™â€¦Ã˜Â§Ã˜Âª Ã™Ë†Ã˜Â§Ã™â€žÃ˜Â£Ã˜Â¯Ã™Ë†Ã˜Â§Ã˜Âª Ã™Ë†Ã˜Â§Ã™â€žÃ˜ÂªÃ˜Â®Ã˜Â·Ã™Å Ã˜Â·.",
    },
    {
      en: "Dashboards",
      pt: "PainÃƒÂ©is",
      es: "Paneloj",
      fr: "Tableaux",
      de: "Dashboards",
      cn: " Ã¤Â»ÂªÃ¨Â¡Â¨Ã¦ÂÂ¿ ",
      ae: "Ã™Ë†Ã˜Â­Ã˜Â§Ã˜Âª Ã˜Â§Ã™â€žÃ™â€šÃ™Å Ã˜Â§Ã˜Â¯Ã˜Â© ",
    },
    {
      en: "Default",
      pt: "PadrÃƒÂ£o",
      es: "Vaikimisi",
      fr: "DÃƒÂ©faut",
      de: "Standaard",
      cn: "Ã©â€ºÂ»Ã¥Â­ÂÃ¥â€¢â€ Ã¥â€¹â„¢",
      ae: "Ã™Ë†Ã˜Â¥Ã™ÂÃ˜ÂªÃ˜Â±Ã˜Â§Ã˜Â¶Ã™Å ",
    },
    {
      en: "Ecommerce",
      pt: "ComÃƒÂ©rcio eletrÃƒÂ´nico",
      es: "Komerco",
      fr: "Commerce ÃƒÂ©lectronique",
      de: "E-commerce",
      cn: "Ã©â€ºÂ»Ã¥Â­ÂÃ¥â€¢â€ Ã¥â€¹â„¢",
      ae: "Ã™Ë†Ã˜Â§Ã™â€žÃ˜ÂªÃ˜Â¬Ã˜Â§Ã˜Â±Ã˜Â© Ã˜Â§Ã™â€žÃ˜Â¥Ã™â€žÃ™Æ’Ã˜ÂªÃ˜Â±Ã™Ë†Ã™â€ Ã™Å Ã˜Â©",
    },
    {
      en: "Widgets",
      pt: "Ferramenta",
      es: "Vidin",
      fr: "Widgets",
      de: "Widgets",
      cn: "Ã¥Â°ÂÃ©Æ’Â¨Ã¤Â»Â¶",
      ae: "Ã™Ë†Ã˜Â§Ã™â€žÃ˜Â­Ã˜Â§Ã˜Â¬Ã™Å Ã˜Â§Ã˜Âª",
    },
    {
      en: "Page layout",
      pt: "Layout da pÃƒÂ¡gina",
      es: "PaÃ„Âa aranÃ„Âo",
      fr: "Tableaux",
      de: "Mise en page",
      cn: "Ã© ÂÃ©ÂÂ¢Ã¤Â½Ë†Ã¥Â±â‚¬",
      ae: "Ã™Ë†Ã˜ÂªÃ˜Â®Ã˜Â·Ã™Å Ã˜Â· Ã˜Â§Ã™â€žÃ˜ÂµÃ™ÂÃ˜Â­Ã˜Â©",
    },
    {
      en: "Applications",
      pt: "FormulÃƒÂ¡rios",
      es: "Aplikoj",
      fr: "Applications",
      de: "Toepassingen",
      cn: "Ã¦â€¡â€°Ã§â€Â¨Ã© ËœÃ¥Å¸Å¸",
      ae: "Ã™Ë†Ã˜Â§Ã™â€žÃ˜ÂªÃ˜Â·Ã˜Â¨Ã™Å Ã™â€šÃ˜Â§Ã˜Âª",
    },
    {
      en: "Ready to use Apps",
      pt: "Pronto para usar aplicativos",
      es: "Preta uzi Apps",
      fr: " Applications prÃƒÂªtes Ãƒ  lemploi ",
      de: "Klaar om apps te gebruiken",
      cn: "Ã¤Â»ÂªÃ¨Â¡Â¨Ã¦ÂÂ¿",
      ae: "Ã˜Â¬Ã˜Â§Ã™â€¡Ã˜Â² Ã™â€žÃ˜Â§Ã˜Â³Ã˜ÂªÃ˜Â®Ã˜Â¯Ã˜Â§Ã™â€¦ Ã˜Â§Ã™â€žÃ˜ÂªÃ˜Â·Ã˜Â¨Ã™Å Ã™â€šÃ˜Â§Ã˜Âª",
    },
  ];

  function translate(tnum) {
    for (var i = 1; i < 9; i++) {
      $(".lan-" + i).text(trans[i - 1][tnum]);
    }
  }

  // Initialize upload field
  $(document).on('change', 'input[type="file"]', function (event) {
    var $file = $(this),
      $label = $file.next('label'),
      $labelText = $label.find('span:first'),
      $typeFileText = $label.find('.type-file-text'),
      labelDefault = "Upload Image";

    var fileName = $file.val().split('\\').pop(),
      file = event.target.files[0],
      fileType = file ? file.type.split('/')[0] : null,
      tmppath = file ? URL.createObjectURL(file) : null;

    if (fileName) {
      if (fileType === "image") {

        $label.addClass('file-ok').css('background-image', 'url(' + tmppath + ')');
      } else {

        $label.addClass('file-ok').css('background-image', 'none');
      }
      $labelText.text(fileName);
      $typeFileText.hide();
      $label.siblings('.file-upload-close').show();
    } else {
      resetUpload($file, $label, $labelText, $typeFileText,
        labelDefault);
    }
  });

  $(document).on('click', '.file-upload-close', function () {
    var $button = $(this),
      $uploadWrapper = $button.closest('.upload-custom-file'),
      $fileInput = $uploadWrapper.find('input[type="file"]'),
      $label = $fileInput.next('label'),
      $labelText = $label.find('span:first'),
      $typeFileText = $label.find('.type-file-text'),
      labelDefault = "Upload Image";

    resetUpload($fileInput, $label, $labelText, $typeFileText, labelDefault);
  });

  function resetUpload($fileInput, $label, $labelText, $typeFileText, labelDefault) {
    $fileInput.val('');
    $label.removeClass('file-ok').css('background-image', 'none');
    $labelText.text(labelDefault);
    $typeFileText.show();
    $label.siblings('.file-upload-close').hide();
  }

  // Initialize Theme Switching
  document.addEventListener("DOMContentLoaded", () => {
    const themeToggleButton = document.getElementById("theme-toggle");
    const body = document.body;

    // Apply saved theme if it exists
    if (localStorage.getItem("theme") === "dark") {
      body.classList.add("dark-theme");
    }

    // Toggle theme on button click
    if (themeToggleButton) {
      themeToggleButton.addEventListener("click", () => {
        const isDark = body.classList.toggle("dark-theme");
        localStorage.setItem("theme", isDark ? "dark" : "");
      });
    }
  });

  // Initialize toasts 
  document.querySelectorAll('.td-alert-box .close-btn').forEach((btn) => {
    btn.addEventListener('click', function () {
      const alertBox = this.closest('.td-alert-box');
      alertBox.classList.add('hidden');
      setTimeout(() => {
        alertBox.style.display = 'none';
      }, 400);
    });
  });

})(jQuery);