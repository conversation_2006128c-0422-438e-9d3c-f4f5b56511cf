<?php $__env->startSection('title'); ?>
    <?php echo e(__('Theme Management')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="main-content">
        <div class="page-title">
            <div class="container-fluid">
                <div class="row">
                    <div class="col">
                        <h2 class="title"><?php echo $__env->yieldContent('theme-title'); ?></h2>
                    </div>
                </div>
            </div>
        </div>
        <div class="container-fluid">
            <div class="row">

                <div class="col-xl-12">
                    <div class="site-tab-bars">
                        <ul>
                            <li class="<?php echo e(isActive('admin.theme.site')); ?>">
                                <a href="<?php echo e(route('admin.theme.site')); ?>"><i
                                        data-lucide="roller-coaster"></i><?php echo e(__('Site Theme')); ?></a>
                            </li>
                            <li class="<?php echo e(isActive('admin.theme.dynamic-landing')); ?>">
                                <a href="<?php echo e(route('admin.theme.dynamic-landing')); ?>"><i
                                        data-lucide="warehouse"></i><?php echo e(__('Site Dynamic Landing Theme')); ?></a>
                            </li>

                        </ul>
                    </div>
                    <div class="row">
                        <?php echo $__env->yieldContent('theme-content'); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/theme/index.blade.php ENDPATH**/ ?>