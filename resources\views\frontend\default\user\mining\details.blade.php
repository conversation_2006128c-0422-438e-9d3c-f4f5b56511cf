@extends('frontend::layouts.user')

@section('title')
   {{ __('Mining Details') }}
@endsection
@use('App\Enums\TxnType')
@use('App\Models\Coin')
@use('App\Enums\TxnStatus')
@use('App\Enums\UserMiningStatus')
@section('content')
   <div class="col-xxl-12">
      <div class="page-title-wrapper mb-16">
        <div class="page-title-contents">
          <h3 class="page-title">{{ __('Mining Details') }}</h3>
        </div>
      </div>
      @include('frontend::user.mining.include.navigation')
   </div>
   <div class="col-xxl-12">
      <div class="mining-history-details-area default-area-style">
         <div class="heading-top">
            <h5 class="title">{{ __('History Details') }}</h5>
         </div>
            <div class="mining-history-details-grid">
               <div class="mining-history-card-wrapper">
                  <div class="mining-history-card">
                     <div class="card-badge">
                        <div class="clip-badge btn-clip grd-outline-fill-btn btn-secondary">
                           <div class="inner-badge">
                              <div class="icon">{{ $transaction->scheme->miner->coin->symbol }}</div>
                              <span class="text">{{ $transaction->scheme->miner->coin->name }}</span>
                           </div>
                        </div>
                     </div>
                     <div class="card-amount">
                        <h4 class="amount" data-bs-toggle="tooltip" title="{{ __('This live calculation is based on the average return amount. The actual amount may vary. Final amount will be updated after next mining.') }}">{{ formatAmount($liveCalData['currentWalletBalance'], $transaction->scheme->miner->coin->code, true) }}</h4>
                        <h5 class="change"> 
                           @if ($transaction->scheme->return_amount_type=='min-max')
                           <small>{{ __('Estimated Earnings') }}</small>
                           <br>
                           @endif
                           {{ ucwords($transaction->userMining?->status) }}</h5>
                     </div>
            
                     <div class="stats-grid">
                        <div class="stat-row">
                           <span class="stat-label">{{ __('Last Mining') }}</span>
                           <span class="stat-value">{{ $transaction->userMining?->last_mining_time ? now()->parse($transaction->userMining?->last_mining_time)->diffForHumans() : 'N/A' }}</span>
                        </div>
                        <div class="stat-row">
                           <span class="stat-label">{{ __('Next Mining') }}</span>
                           <span class="stat-value">{{ $transaction->userMining?->next_mining_time ? now()->parse($transaction->userMining?->next_mining_time)->diffForHumans() : 'N/A' }}</span>
                        </div>
                        <div class="stat-row">
                           <span class="stat-label">{{ __('Total Minned Amount') }}</span>
                           <span class="stat-value">{{ formatAmount($transaction->userMining?->total_mined_amount, $transaction->scheme->miner->coin->code, true) }}</span>
                        </div>
                        <div class="stat-row">
                           <span class="stat-label">{{ __('Speed') }}</span>
                           <span class="stat-value">{{ $transaction->scheme->speed_amount.' '.$transaction->scheme->speed }}</span>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="mining-history-area">
                  <div class="history-grid">
                     <div class="history-row">
                         <span class="history-label">{{ __('Started At') }}</span>
                         <span class="history-value">{{ $transaction->created_at }}</span>
                     </div>
                 
                     <div class="history-row">
                         <span class="history-label">{{ __('Miner') }}</span>
                         <span class="history-value">{{ $transaction->scheme->miner->name }}</span>
                     </div>
                 
                 
                     <div class="history-row">
                         <span class="history-label">{{ __('Return /period') }}</span>
                         <span class="history-value success-value">
                             @if($transaction->scheme->return_amount_type == 'fixed')
                                 {{ formatAmount($transaction->scheme->return_amount_value, $transaction->scheme->miner->coin->code, true) }}
                             @else
                                 {{ formatAmount($transaction->scheme->return_min_amount, $transaction->scheme->miner->coin->code, true) }}
                                 -
                                 {{ formatAmount($transaction->scheme->return_max_amount, $transaction->scheme->miner->coin->code, true) }}
                             @endif
                         </span>
                     </div>
                 
                     <div class="history-row">
                         <span class="history-label">{{ __('Miner Title') }}</span>
                         <span class="history-value">{{ $transaction->scheme->name }}</span>
                     </div>
                 
                     <div class="history-row">
                         <span class="history-label">{{ __('Miner Price') }}</span>
                         <span class="history-value">
                             {{ formatAmount($transaction->scheme->price, setting('site_currency', 'global'), true) }}
                         </span>
                     </div>
                 
                     <div class="history-row">
                         <span class="history-label">{{ __('Total Period') }}</span>
                         <span class="history-value">{{ $transaction->scheme->return_period_type == 'period' ? $transaction->scheme->return_period_max_number : 'Unlimited' }}</span>
                     </div>
                 
                     @if ($transaction->userMining)
                         <div class="history-row">
                             <span class="history-label">{{ __('Mining Count') }}</span>
                             <span class="history-value">{{ $transaction->userMining?->mining_count }}</span>
                         </div>
                         <div class="history-row">
                             <span class="history-label">{{ __('Remaining Period') }}</span>
                             <span class="history-value">
                                 {{ $transaction->scheme->return_period_type == 'period' ? $transaction->scheme?->return_period_max_number - $transaction->userMining?->mining_count : 'Unlimited' }}
                             </span>
                         </div>
                                  
                         <div class="history-row">
                             <span class="history-label">{{ __('Status') }}</span>
                             <span class="history-value">
                                 {{ ucwords($transaction->userMining?->status) }}
                             </span>
                         </div>
                         <div class="history-row">
                             <span class="history-label">{{ __('Transaction ID') }}</span>
                             <span class="history-value">
                                 {{ ($transaction->tnx) }}
                             </span>
                         </div>
                         <div class="history-row">
                             <span class="history-label">{{ __('Amount') }}</span>
                             <span class="history-value">
                                 {{ trxAmountFormat($transaction,'amount') }}
                             </span>
                         </div>
                         <div class="history-row">
                             <span class="history-label">{{ __('Payment Method') }}</span>
                             <span class="history-value">
                                 {{ $transaction->method }}
                             </span>
                         </div>
                     @endif
                 </div>
               </div>
            </div>
      </div>
   </div>
@endsection
@push('script')
   @if (isset($liveCalData['alreadyPassedTime']) && $transaction->userMining?->status == UserMiningStatus::Ongoing->value)
   <script>
      $(document).ready(function () {

         const earningPerPeriod = {!! json_encode($liveCalData['earningPerPeriod']) !!};
         const returnDuration = {{ $liveCalData['returnDuration'] }};
         const alreadyPassedTime = {{ $liveCalData['alreadyPassedTime'] }};
         const remainingTime = {{ $liveCalData['remainingTime'] }};
         const earningPerSecond = {{ $liveCalData['earningPerSecond'] }};
         const currentWalletBalance = {{ $liveCalData['alreadyMinedAmountInBackground'] + $liveCalData['currentWalletBalance'] }};
   
         let elapsedTime = alreadyPassedTime;
         let currentEarnings = currentWalletBalance;
   
         function updateEarnings() {
            if (elapsedTime >= returnDuration) return;
   
            currentEarnings += earningPerSecond;
            elapsedTime += 1;
   
            const maxEarning = (earningPerPeriod[0] + earningPerPeriod[1]) / 2;
            if (currentEarnings > maxEarning) {
               currentEarnings = maxEarning;
            }
   
            $('.card-amount .amount').text(currentEarnings.toFixed(8)+' {{ $transaction->scheme->miner->coin->code }}');
         }
   
         updateEarnings();
   
         setInterval(updateEarnings, 1000);
      });
   </script>
   @endif

@endpush
