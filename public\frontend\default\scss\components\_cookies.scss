@use "../utils" as *;

/*----------------------------------------*/
/* cookies style
/*----------------------------------------*/
.caches-privacy {
    max-width: 1040px;
    position: fixed;
    bottom: 30px;
    inset-inline-start: 50%;
    transform: translateX(-50%);
    row-gap: 12px;
    column-gap: 12px;
    border-radius: 12px;
    box-shadow: 0px 23px 100px 0px rgba(166, 239, 103, 0.16);
    padding: 18px 24px 18px 24px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 90%;
    z-index: 111;
    transition: 0.3s;
    background: var(--td-white);
    border: 1px solid #729CFF;

    @include dark-theme {
        border-color: #26456F;
        background: #0E1B2C;
    }

    @include rtl {
        inset-inline-start: auto;
        inset-inline-end: 50%;
    }

    @media #{$xs,$sm} {
        flex-direction: column;
        align-items: self-start;
    }

    @media #{$xxs} {
        padding: 14px 16px 14px 16px;

        .caches-btns {
            .td-btn {
                font-size: 12px;
            }
        }
    }

    .caches-contents {
        .title {
            font-size: 18px;
            margin-bottom: 8px;
            font-weight: 600;

            @media #{$xxs} {
                font-size: 16px;
            }
        }

        p {
            font-size: 16px;
            margin-bottom: 0;

            @media #{$xxs} {
                font-size: 14px;
            }

            a {
                background-image: linear-gradient(87.17deg, #4776E6 0%, #8E54E9 100%);
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
            }
        }
    }

    .caches-btns {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex: 0 0 auto;
        flex-wrap: wrap;
        gap: 12px;

        .td-btn {
            padding: 0 16px;
            height: 36px;
            font-size: 14px;

            .inner-btn {
                padding: 0 16px;
                font-size: 14px;
            }
        }
    }
}