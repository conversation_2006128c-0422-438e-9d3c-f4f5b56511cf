<?php $__env->startSection('title'); ?>
    <?php echo e(__('Home')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('meta_keywords'); ?>
    <?php echo e(trim(setting('meta_keywords', 'meta'))); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('meta_description'); ?>
    <?php echo e(trim(setting('meta_description', 'meta'))); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <?php $__currentLoopData = $homeContent; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $content): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php
            $data = Fluent(json_decode($content->data, true));
        ?>
        <?php echo $__env->make('frontend::home.include.__' . $content->code, ['data' => $data,'content' => $content->content(),'fromHome' => true], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('script'); ?>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('frontend::layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\app\Providers/../../resources/views/frontend/default/home/<USER>/ ?>