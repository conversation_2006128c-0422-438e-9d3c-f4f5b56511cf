{"__meta": {"id": "01JY3V024R5RDCD972HH56BT1D", "datetime": "2025-06-19 16:15:50", "utime": **********.169317, "method": "GET", "uri": "/user/payment-gateway-info/1", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750328149.8758, "end": **********.169368, "duration": 0.29356813430786133, "duration_str": "294ms", "measures": [{"label": "Booting", "start": 1750328149.8758, "relative_start": 0, "end": **********.095766, "relative_end": **********.095766, "duration": 0.*****************, "duration_str": "220ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.095774, "relative_start": 0.*****************, "end": **********.169375, "relative_end": 6.9141387939453125e-06, "duration": 0.*****************, "duration_str": "73.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.108486, "relative_start": 0.*****************, "end": **********.110263, "relative_end": **********.110263, "duration": 0.0017771720886230469, "duration_str": "1.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: frontend::user.add_money.review_details", "start": **********.164284, "relative_start": 0.****************, "end": **********.164284, "relative_end": **********.164284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.167637, "relative_start": 0.***************, "end": **********.167869, "relative_end": **********.167869, "duration": 0.0002319812774658203, "duration_str": "232μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "31MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.18.0", "PHP Version": "8.3.19", "Environment": "local", "Debug Mode": "Enabled", "URL": "orexcoin.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "frontend::user.add_money.review_details", "param_count": null, "params": [], "start": **********.164264, "type": "blade", "hash": "bladeE:\\laragon\\www\\orexcoin\\app\\Providers/../../resources/views/frontend/default/user/add_money/review_details.blade.phpfrontend::user.add_money.review_details", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Fuser%2Fadd_money%2Freview_details.blade.php:1", "ajax": false, "filename": "review_details.blade.php", "line": "?"}}]}, "queries": {"count": 5, "nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00965, "accumulated_duration_str": "9.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'RlVeXUqfaJFjRpxBhzt1tqTusMxu4a7ScBN9ievH' limit 1", "type": "query", "params": [], "bindings": ["RlVeXUqfaJFjRpxBhzt1tqTusMxu4a7ScBN9ievH"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.114648, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "orexcoin", "explain": null, "start_percent": 0, "width_percent": 4.87}, {"sql": "select * from `users` where `id` = 45 limit 1", "type": "query", "params": [], "bindings": [45], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.1235719, "duration": 0.00563, "duration_str": "5.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "orexcoin", "explain": null, "start_percent": 4.87, "width_percent": 58.342}, {"sql": "select * from `languages` where `is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\orexcoin\\app\\helpers.php", "line": 382}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.131339, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "helpers.php:382", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\orexcoin\\app\\helpers.php", "line": 382}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2Fhelpers.php:382", "ajax": false, "filename": "helpers.php", "line": "382"}, "connection": "orexcoin", "explain": null, "start_percent": 63.212, "width_percent": 3.005}, {"sql": "select * from `deposit_methods` where `status` = 1 and `deposit_methods`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": [1, "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Frontend/AddMoneyController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\AddMoneyController.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.1521091, "duration": 0.0029300000000000003, "duration_str": "2.93ms", "memory": 0, "memory_str": null, "filename": "AddMoneyController.php:63", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Frontend/AddMoneyController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\AddMoneyController.php", "line": 63}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FAddMoneyController.php:63", "ajax": false, "filename": "AddMoneyController.php", "line": "63"}, "connection": "orexcoin", "explain": null, "start_percent": 66.218, "width_percent": 30.363}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Providers\\ViewServiceProvider.php", "line": 67}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 189}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160}], "start": **********.1634731, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ViewServiceProvider.php:67", "source": {"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Providers\\ViewServiceProvider.php", "line": 67}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FProviders%2FViewServiceProvider.php:67", "ajax": false, "filename": "ViewServiceProvider.php", "line": "67"}, "connection": "orexcoin", "explain": null, "start_percent": 96.58, "width_percent": 3.42}]}, "models": {"data": {"App\\Models\\Language": {"value": 2, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\DepositMethod": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FDepositMethod.php:1", "ajax": false, "filename": "DepositMethod.php", "line": "?"}}}, "count": 4, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://orexcoin.test/user/payment-gateway-info/1", "action_name": "user.paymentGateway.info", "controller_action": "App\\Http\\Controllers\\Frontend\\AddMoneyController@gatewayInfo", "uri": "GET user/payment-gateway-info/{id}", "controller": "App\\Http\\Controllers\\Frontend\\AddMoneyController@gatewayInfo<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FAddMoneyController.php:59\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/user", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FAddMoneyController.php:59\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/AddMoneyController.php:59-78</a>", "middleware": "web, auth:web, 2fa, check_deactivation, verified, check_feature:user_deposit,kyc_deposit", "duration": "294ms", "peak_memory": "32MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1939171830 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1939171830\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-416963675 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-416963675\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1974709900 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"735 characters\">declinecookies=true; XSRF-TOKEN=eyJpdiI6IlVFekx5QjV3SHlzbGpEUDRkT0NiUVE9PSIsInZhbHVlIjoidHpKbUw1a3Jud2ZKd3d2MzVPMWo2elZTUXg0WlBZWm95clUvUE56TnZmSmE0S3J3QXNERGk4dG5MQ3lKTzRxRGNURWxoSHZCRS9UaGJWSW0ybjZaRGdqZ0ZxT3hNUjdwNkU5MVNjSkszZUJjVDBCM25FUG1xaDZBVUc2YUk4cmciLCJtYWMiOiI1ZjAwOTRmYTM5ZTUxOTlmYWQxZGNlOGM2MGRkM2U4Y2RlODVlYWEzODk5YTVkOGRlZGNkMWI1MzVjNjgyOWJjIiwidGFnIjoiIn0%3D; orexcoin_session=eyJpdiI6IlhRZjBoNVZRSHlVa3E3cUQ2bjJMWmc9PSIsInZhbHVlIjoiNmxORnR3Qm9ZMTg0cllhODFqMnNEV0tNc0kyWURSZHZnVklXaTRFM1lJc2IvM0ltRU4rblU1Q1ltZ085M01kVnJSUGhheTU5eVZWK252WmdPcm1DU3FkU3ZRVGU2bTVFajE1bDdGUWJidkV6aDZ6U2NwRC9xcUVtcUJGeFdjeEciLCJtYWMiOiI2ZTY0OWNlNDQyYmMyNWMxYjMxMmRlNWI4MDUwYTI4ZDE5Y2Y2NmQwOTQzMDg3ZTA5YjU0MGNlMGJjYThjMmY0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"51 characters\">https://orexcoin.test/user/add-money?user_wallet=15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">orexcoin.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1974709900\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-469382775 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>declinecookies</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GX2dwKYOxZeuJn88ful1fEGUvgF3cILWy6yznC8F</span>\"\n  \"<span class=sf-dump-key>orexcoin_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RlVeXUqfaJFjRpxBhzt1tqTusMxu4a7ScBN9ievH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-469382775\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1608453973 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 19 Jun 2025 10:15:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1608453973\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1267805576 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GX2dwKYOxZeuJn88ful1fEGUvgF3cILWy6yznC8F</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"51 characters\">https://orexcoin.test/user/add-money?user_wallet=15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>theme_mode</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>45</span>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1267805576\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://orexcoin.test/user/payment-gateway-info/1", "action_name": "user.paymentGateway.info", "controller_action": "App\\Http\\Controllers\\Frontend\\AddMoneyController@gatewayInfo"}, "badge": null}}