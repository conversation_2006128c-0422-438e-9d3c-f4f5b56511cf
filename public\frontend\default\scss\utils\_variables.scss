// Color Variables
// Common Color
$white: hsl(0, 0%, 100%);
$black: hsl(0, 0%, 0%);
$placeholder: #9A9DA7;
$selection: hsl(0, 0%, 0%);

// Body Color
$body: #010C1A;

// Heading Color
$heading: #080808;

// Theme Color
$primary: #4776E6 ;
$secondary: #FB405A;
$secondary-alt: #8E54E9;
$eerie-black: #091628;
$void: #020A22;
$liberty-blue: #0C142B;
$td-balaclavas-forever: #16213F;

// Text Color
$text-primary: #47494E;
$text-secondary: #999999;

// Border Color
$border-primary: #eaeaea;

// Others Color
$yellow: #F79E1C;
$warning: #FFA336;
$success: #03A66D;
$danger: #FB405A;
$green: #03A66D;
$info: #2E87E8;

$toastr-success: #1FC54D4D;
$toastr-danger: #EB4E5C1A;
$toastr-warning: #FE84014D;
$toastr-info: #2E87E84D;

// Gradients colors
$gradient-1: #C340C0;
$gradient-2: #FB405A;
$gradient-3: #F7A34A;

// Font Weight Variables  
$fw-normal: normal;
$td-fw-thin: 100;
$td-fw-elight: 200;
$td-fw-light: 300;
$td-fw-regular: 400;
$td-fw-medium: 500;
$td-fw-sbold: 600;
$td-fw-bold: 700;
$td-fw-ebold: 800;
$td-fw-black: 900;

// Font Size Variables  
$td-fs-body: 16px;
$td-fs-p: 16px;
$td-fs-h1: 52px;
$td-fs-h2: 42px;
$td-fs-h3: 32px;
$td-fs-h4: 24px;
$td-fs-h5: 20px;
$td-fs-h6: 16px;

// Responsive Variables

// Ultra-wide screens
$x3l: 'only screen and (min-width: 100rem) and (max-width: 112.49875rem)'; // 1600px - 1799.98px

// Extra Extra Large
$xxl: 'only screen and (min-width: 87.5rem) and (max-width: 99.99875rem)'; // 1400px - 1599.98px

// Extra Large
$xl: 'only screen and (min-width: 75rem) and (max-width: 87.49875rem)'; // 1200px - 1399.98px

// Large
$lg: 'only screen and (min-width: 62rem) and (max-width: 74.99875rem)'; // 992px - 1199.98px

// Medium
$md: 'only screen and (min-width: 48rem) and (max-width: 61.99875rem)'; // 768px - 991.98px

// Small
$sm: 'only screen and (min-width: 36rem) and (max-width: 47.99875rem)'; // 576px - 767.98px

// Extra Small
$xs: 'only screen and (max-width: 35.99875rem)'; // max 575.98px

// Extra Extra Small
$xxs: 'only screen and (max-width: 30.06125rem)'; // max 480.98px

// Inner breakpoints for xs (mobile detail)
$mobile-xxs: 'only screen and (min-width: 0rem) and (max-width: 23.43625rem)'; // 320px - 374.98px
$mobile-xs: 'only screen and (min-width: 23.4375rem) and (max-width: 26.56125rem)'; // 375px - 424.98px
$mobile-sm: 'only screen and (min-width: 26.5625rem) and (max-width: 29.99875rem)'; // 425px - 479.98px
$mobile-md: 'only screen and (min-width: 30rem) and (max-width: 35.99875rem)'; // 480px - 575.98px